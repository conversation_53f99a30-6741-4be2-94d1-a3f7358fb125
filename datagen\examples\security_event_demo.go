package main

import (
	"datagen/config"
	"datagen/engine"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gopkg.in/yaml.v3"
)

// 安全事件数据模板
const securityEventTemplateJSON = `
[
  {
    "Event": {
      "SourceTypeName": "",
      "SourcenodeId": "",
      "TargetTypeName": "",
      "TargetnodeId": "",
      "agentCostTracing": "",
      "agentDisplayTime": "",
      "basicTtp": "",
      "builtinTtpConfidence": "",
      "builtinTtpDescription": "",
      "builtinTtpId": "",
      "builtinTtpObjective": "",
      "builtinTtpSeverity": "",
      "builtinTtpTactic": "",
      "builtinTtpTechnique": "",
      "builtinTtpTitle": "",
      "builtinTtpType": "",
      "callingThreadId": "",
      "cloudSplitEvent": "",
      "controlGraphId": "",
      "currentProcessCount": "",
      "customTtpConfidence": "",
      "customTtpDescription": "",
      "customTtpId": "",
      "customTtpObjective": "",
      "customTtpSeverity": "",
      "customTtpTactic": "",
      "customTtpTechnique": "",
      "customTtpTitle": "",
      "customTtpType": "",
      "dataComponentTypeId": "",
      "driverAutoIncrementId": "",
      "eventId": "",
      "platformUtcTime": "",
      "remoteRpcAddress": "",
      "remoteRpcComputerName": "",
      "remoteRpcPipeName": "",
      "rpcProcessNodeId": "",
      "telemetryAutoIncrementId": "",
      "telemetryEventName": "",
      "telemetryEventTypeId": "",
      "telemetryRuleId": "",
      "tokenType": ""
    },
    "SourceProcess": {
      "appName": "",
      "appType": "",
      "commandLine": "",
      "createdTime": "",
      "creatorNodeId": "",
      "currentDirectory": "",
      "exitTime": "",
      "fileAccessedTime": "",
      "fileAttributes": "",
      "fileCreatedTime": "",
      "fileDescription": "",
      "fileHashMd5": "",
      "fileHashSha1": "",
      "fileHashSha256": "",
      "fileHashSubHash": "",
      "fileModifiedTime": "",
      "fileName": "",
      "fileOriginalFilename": "",
      "filePath": "",
      "fileProduct": "",
      "fileSignatureName": "",
      "fileSignatureTime": "",
      "fileSignatureType": "",
      "fileSize": "",
      "fileVersion": "",
      "imageType": "",
      "integrityLevel": "",
      "isExit": "",
      "isHiddenWindows": "",
      "name": "",
      "nodeId": "",
      "nodeTypeId": "",
      "parentProcessNodeId": "",
      "pid": "",
      "relativeCreatedTime": "",
      "treeId": "",
      "treeNodeId": "",
      "userSessionId": "",
      "userSid": ""
    },
    "TargetProcess": {
      "commandLine": "",
      "createdTime": "",
      "creatorNodeId": "",
      "currentDirectory": "",
      "exitTime": "",
      "fileAccessedTime": "",
      "fileAttributes": "",
      "fileCreatedTime": "",
      "fileDescription": "",
      "fileHashMd5": "",
      "fileHashSha1": "",
      "fileHashSha256": "",
      "fileHashSubHash": "",
      "fileModifiedTime": "",
      "fileName": "",
      "fileOriginalFilename": "",
      "filePath": "",
      "fileProduct": "",
      "fileSignatureName": "",
      "fileSignatureTime": "",
      "fileSignatureType": "",
      "fileSize": "",
      "fileVersion": "",
      "imageType": "",
      "integrityLevel": "",
      "isExit": "",
      "isHiddenWindows": "",
      "name": "",
      "nodeId": "",
      "nodeTypeId": "",
      "parentProcessNodeId": "",
      "pid": "",
      "relativeCreatedTime": "",
      "treeId": "",
      "treeNodeId": "",
      "userSessionId": "",
      "userSid": ""
    },
    "tags": []
  }
]
`

// 新的配置格式
const newSecurityEventConfigYAML = `
rules:
  # Event对象字段生成 - 使用新的配置格式
  - target: "Event.SourceTypeName"
    mode: "upsert"
    action:
      type: "fixedValue"
      value: "Process"
        
  - target: "Event.TargetTypeName"
    mode: "upsert"
    action:
      type: "fixedValue"
      value: "Process"
        
  - target: "Event.SourcenodeId"
    mode: "upsert"
    action:
      type: "randomNumber"
      min: 1000000000
      max: 9999999999
        
  - target: "Event.TargetnodeId"
    mode: "upsert"
    action:
      type: "randomNumber"
      min: 1000000000
      max: 9999999999
        
  - target: "tags"
    mode: "upsert"
    action:
      type: "fixedValue"
      value: ["linkChain"]

  # 测试不同的操作模式
  - target: "Event.NewField"
    mode: "create"  # 只在字段不存在时创建
    action:
      type: "uuid"
      
  - target: "Event.ExistingField"
    mode: "update"  # 只在字段存在时修改
    action:
      type: "timestamp"
      
  - target: "Event.ConditionalField"
    when: "record.Event.SourceTypeName == 'Process'"  # 条件执行
    mode: "upsert"
    action:
      type: "fixedValue"
      value: "ConditionalValue"
`

func main() {
	log.Println("--- New Security Event Data Generation Test Start ---")

	// 1. 解析新格式配置
	var newCfg config.Config
	if err := yaml.Unmarshal([]byte(newSecurityEventConfigYAML), &newCfg); err != nil {
		log.Fatalf("Failed to unmarshal new config YAML: %v", err)
	}
	log.Println("New configuration loaded successfully.")

	// 2. 解析模板数据
	var templates []map[string]any
	if err := json.Unmarshal([]byte(securityEventTemplateJSON), &templates); err != nil {
		log.Fatalf("Failed to unmarshal template JSON: %v", err)
	}
	log.Printf("Loaded %d template records.", len(templates))

	// 3. 创建新引擎实例
	start := time.Now()
	dataEngine, err := engine.NewDataEngine(&newCfg)
	if err != nil {
		log.Fatalf("Failed to create data engine: %v", err)
	}
	log.Printf("Data engine created in %s.", time.Since(start))

	// 4. 执行数据生成
	log.Println("Starting data generation...")
	start = time.Now()
	records, errs := dataEngine.Process(templates)
	if len(errs) > 0 {
		log.Println("Errors occurred during data generation:")
		for _, e := range errs {
			log.Printf("- %v", e)
		}
		return
	}
	log.Printf("Successfully generated %d records in %s.", len(records), time.Since(start))

	// 5. 打印结果
	prettyJSON, err := json.MarshalIndent(records, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal result to JSON: %v", err)
	}

	log.Println("--- Generated Security Event Data ---")
	fmt.Println(string(prettyJSON))

	log.Println("--- Security Event Data Generation Test End ---")
}

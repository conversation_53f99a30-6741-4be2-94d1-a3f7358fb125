package strategy

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"github.com/mitchellh/mapstructure"
)

// DateTimeAfterStrategy 在基准时间上增加随机时间偏移
type DateTimeAfterStrategy struct {
	params *DateTimeAfterParams
}

// DateTimeAfterParams DateTimeAfter策略的参数结构体
type DateTimeAfterParams struct {
	SourceField string `json:"sourceField"`
	MinOffset   string `json:"minOffset"`
	MaxOffset   string `json:"maxOffset"`
}

func (s *DateTimeAfterStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 使用已解析的参数，假设 ValidateParams 已经正确解析
	// 从记录中获取源字段值作为基准时间
	var baseTime int64
	if fieldValue, exists := record[s.params.SourceField]; exists {
		// 尝试将字段值转换为int64（时间戳）
		switch v := fieldValue.(type) {
		case int64:
			baseTime = v
		case int:
			baseTime = int64(v)
		case float64:
			baseTime = int64(v)
		case string:
			// 如果是字符串，尝试解析为时间戳
			parsedTime, err := time.Parse("2006-01-02 15:04:05", v)
			if err != nil {
				// 如果不是标准时间格式，尝试解析为Unix时间戳字符串
				return 0, fmt.Errorf("dateTimeAfter: cannot parse source field value '%s' as time", v)
			}
			baseTime = parsedTime.UnixMilli()
		default:
			return 0, fmt.Errorf("dateTimeAfter: source field value must be a number or time string, got %T", fieldValue)
		}
	} else {
		// 字段不存在，使用当前时间作为基准
		baseTime = time.Now().UnixMilli()
	}

	minOffset := time.Duration(0)
	if s.params.MinOffset != "" {
		var err error
		minOffset, err = time.ParseDuration(s.params.MinOffset)
		if err != nil {
			return nil, fmt.Errorf("dateTimeAfter: invalid 'minOffset' duration '%s': %w", s.params.MinOffset, err)
		}
	}

	maxOffset := time.Minute * 5 // 默认最大偏移5分钟
	if s.params.MaxOffset != "" {
		var err error
		maxOffset, err = time.ParseDuration(s.params.MaxOffset)
		if err != nil {
			return nil, fmt.Errorf("dateTimeAfter: invalid 'maxOffset' duration '%s': %w", s.params.MaxOffset, err)
		}
	}

	// 确保最小偏移不大于最大偏移
	if minOffset > maxOffset {
		return nil, fmt.Errorf("dateTimeAfter: 'minOffset' cannot be greater than 'maxOffset'")
	}

	// 计算偏移范围
	offsetRange := int64(maxOffset - minOffset)
	if offsetRange <= 0 {
		// 如果范围为0，直接使用最小偏移
		return time.Unix(0, baseTime*1e6+int64(minOffset)).UnixMilli(), nil
	}

	// 生成随机偏移
	n, err := rand.Int(rand.Reader, big.NewInt(offsetRange))
	if err != nil {
		return nil, fmt.Errorf("dateTimeAfter: failed to generate random offset: %w", err)
	}
	randomOffset := minOffset + time.Duration(n.Int64())

	// 计算最终时间
	finalTime := time.Unix(0, baseTime*1e6+int64(randomOffset)).UnixMilli()

	return finalTime, nil
}

func (s *DateTimeAfterStrategy) ValidateParams(params map[string]any) error {
	var p DateTimeAfterParams

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("dateTimeAfter: failed to decode params: %w", err)
	}

	// 验证参数
	if p.SourceField == "" {
		return fmt.Errorf("dateTimeAfter: 'sourceField' parameter is required")
	}

	// 验证 minOffset 参数（可选）
	if p.MinOffset != "" {
		// 尝试解析持续时间
		_, err := time.ParseDuration(p.MinOffset)
		if err != nil {
			return fmt.Errorf("dateTimeAfter: invalid 'minOffset' duration format '%s': %w", p.MinOffset, err)
		}
	}

	// 验证 maxOffset 参数（可选）
	if p.MaxOffset != "" {
		// 尝试解析持续时间
		_, err := time.ParseDuration(p.MaxOffset)
		if err != nil {
			return fmt.Errorf("dateTimeAfter: invalid 'maxOffset' duration format '%s': %w", p.MaxOffset, err)
		}
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

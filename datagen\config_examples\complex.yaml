# 复杂的数据生成配置示例
rules:
  # 用户基本信息
  - target: "user.id"
    mode: "create"
    action:
      type: "uuid"
      
  - target: "user.name"
    mode: "upsert"
    action:
      type: "enum"
      values: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
      
  - target: "user.email"
    mode: "upsert"
    action:
      type: "expression"
      expression: "record.user.name + '@example.com'"
      
  # 系统信息
  - target: "system.hostname"
    mode: "upsert"
    action:
      type: "prefixString"
      prefix: "host-"
      
  - target: "system.ip"
    mode: "upsert"
    action:
      type: "ipv4"
      
  - target: "system.os"
    mode: "upsert"
    action:
      type: "weighted_enum"
      values: ["Windows", "Linux", "macOS"]
      weights: [50, 30, 20]
      
  # 文件路径（根据操作系统）
  - target: "file.path"
    when: "record.system.os == 'Windows'"
    mode: "upsert"
    action:
      type: "filePath"
      os: "windows"
      depth: 3
      type: "file"
      
  - target: "file.path"
    when: "record.system.os == 'Linux'"
    mode: "upsert"
    action:
      type: "filePath"
      os: "linux"
      depth: 2
      type: "file"
      
  # 安全相关
  - target: "security.hash"
    mode: "upsert"
    action:
      type: "hash"
      algorithm: "sha256"
      mode: "real"
      source_field: "file.path"
      
  - target: "security.level"
    when: "record.user.name == 'Alice'"  # 管理员用户
    mode: "upsert"
    action:
      type: "fixedValue"
      value: "admin"
      
  - target: "security.level"
    when: "record.user.name != 'Alice'"  # 普通用户
    mode: "upsert"
    action:
      type: "fixedValue"
      value: "user"
      
  # 时间戳
  - target: "created_at"
    mode: "create"
    action:
      type: "timestamp"
      
  - target: "updated_at"
    mode: "upsert"
    action:
      type: "dateTimeAfter"
      base_field: "created_at"
      min_seconds: 3600
      max_seconds: 86400

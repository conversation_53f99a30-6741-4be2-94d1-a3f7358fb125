package strategy

import (
	"fmt"
	"math/rand"
	"strings"

	"github.com/expr-lang/expr"
	"github.com/expr-lang/expr/vm"
	"github.com/mitchellh/mapstructure"
)

// ExpressionStrategy 使用 expr 库执行表达式求值
type ExpressionStrategy struct {
	params          *ExpressionParams
	compiledProgram *vm.Program    // 预编译的表达式程序
	utilityFuncs    map[string]any // 预定义的工具函数
}

// ExpressionParams Expression策略的参数结构体
type ExpressionParams struct {
	Expression string `json:"expression"`
}

func (s *ExpressionStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 构建运行时环境，合并记录数据和工具函数
	env := make(map[string]any, len(record)+len(s.utilityFuncs))

	// 复制记录数据
	for k, v := range record {
		env[k] = v
	}

	// 添加预定义的工具函数
	for k, v := range s.utilityFuncs {
		env[k] = v
	}

	// 使用预编译的程序执行表达式
	result, err := expr.Run(s.compiledProgram, env)
	if err != nil {
		return nil, fmt.Errorf("expression: failed to execute expression '%s': %w", s.params.Expression, err)
	}

	return result, nil
}

func (s *ExpressionStrategy) ValidateParams(params map[string]any) error {
	var p ExpressionParams

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("expression: failed to decode params: %w", err)
	}

	// 验证参数
	if p.Expression == "" {
		return fmt.Errorf("expression: 'expression' parameter is required")
	}

	// 初始化工具函数（一次性）
	s.utilityFuncs = map[string]any{
		"basename": func(path string) string {
			if path == "" {
				return ""
			}
			// 简单的basename实现，支持Windows路径
			parts := strings.Split(path, "\\")
			return parts[len(parts)-1]
		},
		"random": func(min, max int) int {
			return rand.Intn(max-min+1) + min
		},
	}

	// 构建编译环境，包含所有可能的变量类型
	compileEnv := map[string]any{
		"record":            map[string]any{},
		"global_context":    map[string]any{},
		"array_context":     map[string]any{},
		"context":           map[string]any{},
		"index":             0,
		"__array_index__":   0,
		"__array_context__": map[string]any{},
	}

	// 添加工具函数到编译环境
	for k, v := range s.utilityFuncs {
		compileEnv[k] = v
	}

	// 预编译表达式（一次性）
	compiledProgram, err := expr.Compile(p.Expression, expr.Env(compileEnv))
	if err != nil {
		return fmt.Errorf("expression: invalid expression syntax '%s': %w", p.Expression, err)
	}

	// 存储预编译结果
	s.params = &p
	s.compiledProgram = compiledProgram
	return nil
}

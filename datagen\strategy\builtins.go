package strategy

// init 函数在包加载时自动执行，用于注册所有内置策略。
func init() {
	Register("uuid", func() Strategy { return &UUIDStrategy{} })
	Register("timestamp", func() Strategy { return &TimestampStrategy{} })
	Register("randomInt", func() Strategy { return &RandomIntStrategy{} })
	Register("ipv4", func() Strategy { return &IPv4Strategy{} })
	Register("enum", func() Strategy { return &EnumStrategy{} })
	Register("weighted_enum", func() Strategy { return &WeightedEnumStrategy{} })
	Register("hash", func() Strategy { return &HashStrategy{} })
	Register("expression", func() Strategy { return &ExpressionStrategy{} })
	Register("filePath", func() Strategy { return &FilePathStrategy{} })
	Register("dateTimeAfter", func() Strategy { return &DateTimeAfterStrategy{} })
	Register("fixedValue", func() Strategy { return &FixedValueStrategy{} })
	Register("randomString", func() Strategy { return &RandomStringStrategy{} })
	Register("randomNumber", func() Strategy { return &RandomNumberStrategy{} })
	Register("prefixString", func() Strategy { return &PrefixStringStrategy{} })
}

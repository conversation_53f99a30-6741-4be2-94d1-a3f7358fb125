package strategy

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"strings"

	"github.com/mitchellh/mapstructure"
)

// FilePathStrategy 生成符合特定操作系统语法的真实感文件路径
type FilePathStrategy struct {
	params *FilePathParams
}

// FilePathParams FilePath策略的参数结构体
type FilePathParams struct {
	OS        string `json:"os"`
	Depth     int    `json:"depth"`
	BaseName  string `json:"baseName"`
	Extension string `json:"extension"`
	Type      string `json:"type"`
}

func (s *FilePathStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 使用已解析的参数，假设 ValidateParams 已经正确解析
	return s.generateFilePath(s.params.OS, s.params.Depth, s.params.BaseName, s.params.Extension, s.params.Type)
}

func (s *FilePathStrategy) generateFilePath(osType string, depth int, baseName, extension, fileType string) (string, error) {
	// Windows路径模板
	windowsDirs := []string{
		"C:\\Program Files\\",
		"C:\\Program Files (x86)\\",
		"C:\\Windows\\System32\\",
		"C:\\Users\\<USER>\\AppData\\Local\\",
		"C:\\Users\\<USER>\\Desktop\\",
		"C:\\Users\\<USER>\\Documents\\",
		"%TEMP%\\",
		"C:\\ProgramData\\",
	}

	// Linux路径模板
	linuxDirs := []string{
		"/usr/bin/",
		"/usr/sbin/",
		"/usr/local/bin/",
		"/var/log/",
		"/var/www/",
		"/home/<USER>/",
		"/tmp/",
		"/etc/",
		"/opt/",
	}

	// Windows文件名
	windowsFiles := []string{
		"explorer.exe", "chrome.exe", "firefox.exe", "cmd.exe", "powershell.exe",
		"notepad.exe", "mspaint.exe", "winword.exe", "excel.exe", "outlook.exe",
		"svchost.exe", "lsass.exe", "wininit.exe", "services.exe",
	}

	// Linux文件名
	linuxFiles := []string{
		"bash", "sh", "python", "perl", "java", "gcc", "g++",
		"sshd", "apache2", "nginx", "mysql", "postgres",
		"systemd", "cron", "logrotate", "rsync",
	}

	// Windows扩展名
	windowsExtensions := []string{
		".exe", ".dll", ".sys", ".drv", ".ocx",
		".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".txt", ".log", ".ini", ".cfg", ".conf",
		".jpg", ".png", ".gif", ".bmp", ".ico",
	}

	// Linux扩展名
	linuxExtensions := []string{
		"", ".sh", ".py", ".pl", ".rb", ".js",
		".conf", ".config", ".log", ".txt", ".md",
		".so", ".a", ".o", ".la",
	}

	var dirs []string
	var files []string
	var extensions []string

	switch osType {
	case "linux":
		dirs = linuxDirs
		files = linuxFiles
		extensions = linuxExtensions
	default: // windows
		dirs = windowsDirs
		files = windowsFiles
		extensions = windowsExtensions
	}

	// 生成路径
	if depth <= 0 {
		depth = 1
	}
	if depth > 5 {
		depth = 5
	}

	// 随机选择基础目录
	n, err := rand.Int(rand.Reader, big.NewInt(int64(len(dirs))))
	if err != nil {
		return "", fmt.Errorf("failed to select base directory: %w", err)
	}
	baseDir := dirs[n.Int64()]

	// 生成子目录路径
	subDirs := make([]string, 0, depth-1)
	for i := 0; i < depth-1; i++ {
		subDirNames := []string{
			"temp", "cache", "data", "config", "logs", "bin", "lib", "share",
			"app", "system", "user", "admin", "common", "local", "public",
		}
		n2, err := rand.Int(rand.Reader, big.NewInt(int64(len(subDirNames))))
		if err != nil {
			return "", fmt.Errorf("failed to select sub directory: %w", err)
		}
		subDirs = append(subDirs, subDirNames[n2.Int64()])
	}

	// 构建完整路径
	path := baseDir
	for _, subDir := range subDirs {
		path += subDir + "\\"
		if osType == "linux" {
			path = baseDir
			for _, subDir := range subDirs {
				path += subDir + "/"
			}
			break
		}
	}

	// 处理文件名
	var fileName string
	if baseName != "" {
		fileName = baseName
	} else {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(files))))
		if err != nil {
			return "", fmt.Errorf("failed to select file name: %w", err)
		}
		fileName = files[n.Int64()]
	}

	// 处理扩展名
	if extension != "" {
		if !strings.HasPrefix(extension, ".") {
			extension = "." + extension
		}
		fileName += extension
	} else if fileType == "file" && len(extensions) > 0 {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(extensions))))
		if err != nil {
			return "", fmt.Errorf("failed to select extension: %w", err)
		}
		fileName += extensions[n.Int64()]
	}

	// 构建最终路径
	if osType == "linux" {
		// 替换Windows风格的路径分隔符
		path = strings.ReplaceAll(path, "\\", "/")
		// 替换用户名占位符
		path = strings.ReplaceAll(path, "%USERNAME%", "user")
		path = strings.ReplaceAll(path, "%USER%", "user")
		// 确保路径以/开头
		if !strings.HasPrefix(path, "/") {
			path = "/" + path
		}
	} else {
		// 替换用户名占位符
		path = strings.ReplaceAll(path, "%USERNAME%", "User")
		// 替换临时目录占位符
		path = strings.ReplaceAll(path, "%TEMP%", "C:\\Windows\\Temp")
	}

	if fileType == "file" {
		path += fileName
	}

	return path, nil
}

func (s *FilePathStrategy) ValidateParams(params map[string]any) error {
	var p FilePathParams

	// 设置默认值
	p.OS = "windows"
	p.Depth = 3
	p.Type = "file"

	// 使用 mapstructure 进行参数转换，支持弱类型转换
	decoder, err := mapstructure.NewDecoder(&mapstructure.DecoderConfig{
		Result:           &p,
		WeaklyTypedInput: true,
		TagName:          "json",
	})
	if err != nil {
		return fmt.Errorf("filePath: failed to create decoder: %w", err)
	}

	if err := decoder.Decode(params); err != nil {
		return fmt.Errorf("filePath: failed to decode params: %w", err)
	}

	// 验证参数
	if p.OS != "windows" && p.OS != "linux" {
		return fmt.Errorf("filePath: 'os' must be 'windows' or 'linux'")
	}

	if p.Depth < 1 || p.Depth > 10 {
		return fmt.Errorf("filePath: 'depth' must be between 1 and 10")
	}

	if p.Type != "file" && p.Type != "directory" {
		return fmt.Errorf("filePath: 'type' must be 'file' or 'directory'")
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

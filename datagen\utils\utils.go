package utils

import (
	"fmt"
	"strings"

	"github.com/jinzhu/copier"
)

// DeepCopy 使用 copier 库来实现对 map[string]any 的深拷贝。
// 这比JSON序列化方法更高效，避免了编解码开销。
func DeepCopy(data map[string]any) (map[string]any, error) {
	if data == nil {
		return nil, nil
	}

	copied := make(map[string]any, len(data))
	if err := copier.CopyWithOption(&copied, &data, copier.Option{DeepCopy: true}); err != nil {
		return nil, fmt.Errorf("failed to deep copy: %w", err)
	}
	return copied, nil
}

// SetNestedValue 根据点分路径在嵌套的 map 中设置值。
// 如果路径中的中间map不存在，它会自动创建它们。
func SetNestedValue(data map[string]any, path []string, value any) error {
	if len(path) == 0 {
		return fmt.Errorf("path cannot be empty")
	}

	current := data
	// 遍历到路径的倒数第二个元素
	for i := 0; i < len(path)-1; i++ {
		key := path[i]
		next, exists := current[key]
		if !exists {
			// 如果路径不存在，创建新的map
			newMap := make(map[string]any)
			current[key] = newMap
			current = newMap
			continue
		}

		if nextMap, ok := next.(map[string]any); ok {
			// 如果存在且是map，则继续深入
			current = nextMap
		} else {
			// 如果存在但不是map，则路径冲突
			return fmt.Errorf("path conflict: key '%s' in path '%s' is not a map", key, strings.Join(path, "."))
		}
	}

	// 设置最后一个元素的值
	lastKey := path[len(path)-1]
	current[lastKey] = value
	return nil
}

// GetNestedValue 根据点分路径从嵌套的 map 中获取值。
// 如果路径不存在，返回错误。
func GetNestedValue(data map[string]any, path []string) (any, error) {
	if len(path) == 0 {
		return nil, fmt.Errorf("path cannot be empty")
	}

	current := data
	// 遍历到路径的倒数第二个元素
	for i := 0; i < len(path)-1; i++ {
		key := path[i]
		next, exists := current[key]
		if !exists {
			return nil, fmt.Errorf("path '%s' not found", strings.Join(path[:i+1], "."))
		}

		if nextMap, ok := next.(map[string]any); ok {
			// 如果存在且是map，则继续深入
			current = nextMap
		} else {
			// 如果存在但不是map，则路径冲突
			return nil, fmt.Errorf("path conflict: key '%s' in path '%s' is not a map", key, strings.Join(path, "."))
		}
	}

	// 获取最后一个元素的值
	lastKey := path[len(path)-1]
	value, exists := current[lastKey]
	if !exists {
		return nil, fmt.Errorf("path '%s' not found", strings.Join(path, "."))
	}

	return value, nil
}

// IsEmpty 检查值是否为空
func IsEmpty(value any) bool {
	if value == nil {
		return true
	}

	switch v := value.(type) {
	case string:
		return v == ""
	case []any:
		return len(v) == 0
	case map[string]any:
		return len(v) == 0
	case int, int8, int16, int32, int64:
		return v == 0
	case uint, uint8, uint16, uint32, uint64:
		return v == 0
	case float32, float64:
		return v == 0.0
	case bool:
		return !v
	default:
		return false
	}
}

// PathExists 检查路径是否存在
func PathExists(data map[string]any, path []string) bool {
	_, err := GetNestedValue(data, path)
	return err == nil
}

// MergeMaps 合并多个map，后面的会覆盖前面的
func MergeMaps(maps ...map[string]any) map[string]any {
	result := make(map[string]any)

	for _, m := range maps {
		for k, v := range m {
			result[k] = v
		}
	}

	return result
}

package config

import (
	"datagen/strategy"
	"fmt"
	"strings"

	"github.com/expr-lang/expr"
	"github.com/expr-lang/expr/vm"
)

// OperationMode 定义字段操作模式
type OperationMode string

const (
	ModeCreate OperationMode = "create" // 只在字段不存在时创建
	ModeUpdate OperationMode = "update" // 只在字段存在时修改
	ModeUpsert OperationMode = "upsert" // 存在则修改，不存在则创建（默认）
	ModeSkip   OperationMode = "skip"   // 字段不存在时跳过
)

// Config 重构后的配置结构
type Config struct {
	Rules []Rule `yaml:"rules"`
}

// Rule 重构后的规则结构，职责更清晰
type Rule struct {
	Target  string        `yaml:"target"`            // 目标字段路径
	When    string        `yaml:"when,omitempty"`    // 执行条件（可选）
	Mode    OperationMode `yaml:"mode,omitempty"`    // 操作模式（可选，默认upsert）
	Action  ActionSpec    `yaml:"action"`            // 动作配置
	Context ContextSpec   `yaml:"context,omitempty"` // 上下文配置（可选）
}

// ActionSpec 动作配置
type ActionSpec struct {
	Type   string         `yaml:"type"`    // 策略类型
	Params map[string]any `yaml:",inline"` // 参数（内联到action中）
}

// ContextSpec 上下文配置，用于复杂场景
type ContextSpec struct {
	Variables map[string]ActionSpec `yaml:"variables,omitempty"` // 上下文变量定义
}

// PreparedConfig 是引擎直接使用的、经过优化的配置
type PreparedConfig struct {
	Rules []PreparedRule
}

// PreparedRule 包含了预处理过的信息，执行效率更高
type PreparedRule struct {
	TargetPath    []string
	Condition     *vm.Program
	Mode          OperationMode
	ValueStrategy strategy.Strategy
	ActionParams  map[string]any
	Context       map[string]any // 预处理的上下文变量
}

// Prepare 将配置转换为预编译的、高效的配置
func Prepare(cfg *Config) (*PreparedConfig, error) {
	preparedRules := make([]PreparedRule, 0, len(cfg.Rules))

	for i, rule := range cfg.Rules {
		// 1. 预解析路径
		if rule.Target == "" {
			return nil, fmt.Errorf("rule %d: target cannot be empty", i)
		}
		targetPath := strings.Split(rule.Target, ".")

		// 2. 预编译条件
		var compiledCond *vm.Program
		if rule.When != "" {
			var err error
			// 允许使用 record.field 访问记录中的值
			// 提供环境信息，让 expr 知道 record 变量的类型
			env := map[string]any{
				"record":  map[string]any{},
				"context": map[string]any{},
			}
			compiledCond, err = expr.Compile(rule.When, expr.Env(env))
			if err != nil {
				return nil, fmt.Errorf("rule %d: failed to compile condition '%s': %w", i, rule.When, err)
			}
		}

		// 3. 设置默认操作模式
		mode := rule.Mode
		if mode == "" {
			mode = ModeUpsert // 默认为upsert模式
		}

		// 4. 预获取策略
		if rule.Action.Type == "" {
			return nil, fmt.Errorf("rule %d: action.type cannot be empty", i)
		}
		strat, err := strategy.CreateStrategy(rule.Action.Type)
		if err != nil {
			return nil, fmt.Errorf("rule %d: %w", i, err)
		}

		// 5. 验证策略参数
		if err := strat.ValidateParams(rule.Action.Params); err != nil {
			return nil, fmt.Errorf("rule %d: failed to validate parameters for strategy '%s': %w", i, rule.Action.Type, err)
		}

		// 6. 预处理上下文变量
		context := make(map[string]any)
		for varName, varAction := range rule.Context.Variables {
			varStrat, err := strategy.CreateStrategy(varAction.Type)
			if err != nil {
				return nil, fmt.Errorf("rule %d: failed to create context variable '%s' strategy: %w", i, varName, err)
			}
			if err := varStrat.ValidateParams(varAction.Params); err != nil {
				return nil, fmt.Errorf("rule %d: failed to validate context variable '%s' parameters: %w", i, varName, err)
			}
			context[varName] = map[string]any{
				"strategy": varStrat,
				"params":   varAction.Params,
			}
		}

		preparedRules = append(preparedRules, PreparedRule{
			TargetPath:    targetPath,
			Condition:     compiledCond,
			Mode:          mode,
			ValueStrategy: strat,
			ActionParams:  rule.Action.Params,
			Context:       context,
		})
	}

	return &PreparedConfig{Rules: preparedRules}, nil
}

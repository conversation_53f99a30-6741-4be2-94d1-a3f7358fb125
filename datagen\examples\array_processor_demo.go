package main

import (
	"datagen/config"
	"datagen/engine"
	_ "datagen/strategy" // 导入以执行init()函数，注册内置策略
	"encoding/json"
	"fmt"
	"log"

	"gopkg.in/yaml.v3"
)

// 数组数据模板
const arrayTemplateJSON = `
{
	"users": [
		{
			"id": "",
			"name": "",
			"email": "",
			"age": 0,
			"department": ""
		},
		{
			"id": "",
			"name": "",
			"email": "",
			"age": 0,
			"department": ""
		},
		{
			"id": "",
			"name": "",
			"email": "",
			"age": 0,
			"department": ""
		}
	],
	"metadata": {
		"total": 0,
		"generated_at": ""
	}
}
`

// 新的数组处理配置
const arrayProcessorConfigYAML = `
rules:
  # 处理users数组
  - target: "users"
    action:
      type: "arrayProcessor"
      array_field: "users"  # 指定数组字段
      array_context:
        # 数组级别的上下文变量
        company_id:
          type: "randomNumber"
          min: 1000
          max: 9999
        department_pool:
          type: "enum"
          values: ["Engineering", "Marketing", "Sales", "HR", "Finance"]
        base_email_domain:
          type: "fixedValue"
          value: "company.com"
      item_rules:
        # 为每个用户生成数据
        - target: "id"
          mode: "upsert"
          action:
            type: "uuid"
            
        - target: "name"
          mode: "upsert"
          action:
            type: "enum"
            values: ["Alice", "Bob", "Charlie", "Diana", "Eve", "Frank", "Grace", "Henry"]
            
        - target: "email"
          mode: "upsert"
          action:
            type: "expression"
            expression: "record.name + '@' + array_context.base_email_domain"
            
        - target: "age"
          mode: "upsert"
          action:
            type: "randomNumber"
            min: 22
            max: 65
            
        - target: "department"
          mode: "upsert"
          action:
            type: "expression"
            expression: "array_context.department_pool"
            
        - target: "employee_id"
          mode: "create"  # 只在字段不存在时创建
          action:
            type: "expression"
            expression: "array_context.company_id * 1000 + index"

  # 更新元数据
  - target: "metadata.total"
    mode: "upsert"
    action:
      type: "fixedValue"
      value: 3
      
  - target: "metadata.generated_at"
    mode: "upsert"
    action:
      type: "timestamp"
`

func main() {
	log.Println("--- Array Processor Demo ---")

	// 1. 解析配置
	var cfg config.Config
	if err := yaml.Unmarshal([]byte(arrayProcessorConfigYAML), &cfg); err != nil {
		log.Fatalf("Failed to unmarshal config: %v", err)
	}
	log.Println("Array processor configuration loaded successfully.")

	// 2. 解析模板数据
	var template map[string]any
	if err := json.Unmarshal([]byte(arrayTemplateJSON), &template); err != nil {
		log.Fatalf("Failed to unmarshal template: %v", err)
	}
	log.Println("Template loaded successfully.")

	// 3. 创建引擎
	dataEngine, err := engine.NewDataEngine(&cfg)
	if err != nil {
		log.Fatalf("Failed to create data engine: %v", err)
	}

	// 4. 生成数据
	result, err := dataEngine.Generate(template)
	if err != nil {
		log.Fatalf("Failed to generate data: %v", err)
	}

	// 5. 打印结果
	prettyJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal result: %v", err)
	}

	log.Println("--- Generated Array Data ---")
	fmt.Println(string(prettyJSON))

	// 6. 演示直接处理数组
	log.Println("\n--- Direct Array Processing Demo ---")
	testDirectArrayProcessing()
}

func testDirectArrayProcessing() {
	// 直接处理数组的配置
	directArrayConfigYAML := `
rules:
  - target: "."  # 处理整个record作为数组容器
    action:
      type: "arrayProcessor"
      array_context:
        sequence_start:
          type: "randomNumber"
          min: 1000
          max: 2000
      item_rules:
        - target: "id"
          mode: "upsert"
          action:
            type: "expression"
            expression: "array_context.sequence_start + index"
        - target: "status"
          mode: "create"
          action:
            type: "enum"
            values: ["active", "inactive", "pending"]
`

	// 数组模板
	arrayTemplate := map[string]any{
		"items": []map[string]any{
			{"id": 0, "name": "Item 1"},
			{"id": 0, "name": "Item 2"},
			{"id": 0, "name": "Item 3"},
		},
	}

	// 解析配置
	var cfg config.Config
	if err := yaml.Unmarshal([]byte(directArrayConfigYAML), &cfg); err != nil {
		log.Fatalf("Failed to unmarshal direct array config: %v", err)
	}

	// 创建引擎
	dataEngine, err := engine.NewDataEngine(&cfg)
	if err != nil {
		log.Fatalf("Failed to create data engine: %v", err)
	}

	// 生成数据
	result, err := dataEngine.Generate(arrayTemplate)
	if err != nil {
		log.Fatalf("Failed to generate direct array data: %v", err)
	}

	// 打印结果
	prettyJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Println(string(prettyJSON))
}

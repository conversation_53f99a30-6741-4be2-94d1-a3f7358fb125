package strategy

import (
	"crypto/rand"
	"fmt"
	"math/big"

	"github.com/mitchellh/mapstructure"
)

// RandomStringStrategy 生成一个指定长度范围内的随机字符串
type RandomStringStrategy struct {
	params *RandomStringParams
}

// RandomStringParams RandomString策略的参数结构体
type RandomStringParams struct {
	MinLength int    `json:"minLength"`
	MaxLength int    `json:"maxLength"`
	Charset   string `json:"charset"` // 字符集，如 "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
}

func (s *RandomStringStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 首先生成随机长度
	if s.params.MinLength == s.params.MaxLength {
		return s.generateRandomString(s.params.MinLength)
	}

	lengthRange := s.params.MaxLength - s.params.MinLength + 1
	n, err := rand.Int(rand.Reader, big.NewInt(int64(lengthRange)))
	if err != nil {
		return nil, fmt.Errorf("failed to generate random length: %w", err)
	}

	length := int(n.Int64()) + s.params.MinLength
	return s.generateRandomString(length)
}

func (s *RandomStringStrategy) generateRandomString(length int) (string, error) {
	if length <= 0 {
		return "", nil
	}

	charset := s.params.Charset
	if charset == "" {
		// 默认字符集：字母+数字
		charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	}

	result := make([]byte, length)
	charsetLength := big.NewInt(int64(len(charset)))

	for i := 0; i < length; i++ {
		n, err := rand.Int(rand.Reader, charsetLength)
		if err != nil {
			return "", fmt.Errorf("failed to generate random character: %w", err)
		}
		result[i] = charset[n.Int64()]
	}

	return string(result), nil
}

func (s *RandomStringStrategy) ValidateParams(params map[string]any) error {
	var p RandomStringParams

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("randomString: failed to decode params: %w", err)
	}

	// 验证参数
	if p.MinLength <= 0 {
		return fmt.Errorf("randomString: 'minLength' must be greater than 0")
	}
	if p.MaxLength <= 0 {
		return fmt.Errorf("randomString: 'maxLength' must be greater than 0")
	}
	if p.MinLength > p.MaxLength {
		return fmt.Errorf("randomString: 'minLength' cannot be greater than 'maxLength'")
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

package strategy

import (
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/expr-lang/expr"
	"github.com/google/uuid"
)

// init 函数在包加载时自动执行，用于注册所有内置策略。
func init() {
	Register("uuid", &UUIDStrategy{})
	Register("timestamp", &TimestampStrategy{})
	Register("randomInt", &RandomIntStrategy{})
	Register("ipv4", &IPv4Strategy{})
	Register("enum", &EnumStrategy{})
	Register("weighted_enum", &WeightedEnumStrategy{})
	Register("hash", &HashStrategy{})
	Register("expression", &ExpressionStrategy{})
	Register("filePath", &FilePathStrategy{})
	Register("dateTimeAfter", &DateTimeAfterStrategy{})
}

// --- 策略实现 ---

// UUIDStrategy 生成一个UUID字符串。
type UUIDStrategy struct{}

func (s *UUIDStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	return uuid.NewString(), nil
}

func (s *UUIDStrategy) ValidateParams(params map[string]any) error {
	// UUID策略不需要参数
	return nil
}

// FilePathStrategy 生成符合特定操作系统语法的真实感文件路径
type FilePathStrategy struct{}

func (s *FilePathStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 参数验证已在 ValidateParams 中完成，这里直接使用
	osType := "windows" // 默认操作系统
	if os, ok := params["os"].(string); ok {
		osType = os
	}

	depth := 3 // 默认深度
	if d, ok := params["depth"].(int); ok {
		depth = d
	} else if dFloat, ok := params["depth"].(float64); ok {
		depth = int(dFloat)
	}

	baseName := ""
	if bn, ok := params["baseName"].(string); ok {
		baseName = bn
	}

	extension := ""
	if ext, ok := params["extension"].(string); ok {
		extension = ext
	}

	fileType := "file" // 默认类型
	if ft, ok := params["type"].(string); ok {
		fileType = ft
	}

	return s.generateFilePath(osType, depth, baseName, extension, fileType)
}

func (s *FilePathStrategy) generateFilePath(osType string, depth int, baseName, extension, fileType string) (string, error) {
	// Windows路径模板
	windowsDirs := []string{
		"C:\\Program Files\\",
		"C:\\Program Files (x86)\\",
		"C:\\Windows\\System32\\",
		"C:\\Users\\<USER>\\AppData\\Local\\",
		"C:\\Users\\<USER>\\Desktop\\",
		"C:\\Users\\<USER>\\Documents\\",
		"%TEMP%\\",
		"C:\\ProgramData\\",
	}

	// Linux路径模板
	linuxDirs := []string{
		"/usr/bin/",
		"/usr/sbin/",
		"/usr/local/bin/",
		"/var/log/",
		"/var/www/",
		"/home/<USER>/",
		"/tmp/",
		"/etc/",
		"/opt/",
	}

	// Windows文件名
	windowsFiles := []string{
		"explorer.exe", "chrome.exe", "firefox.exe", "cmd.exe", "powershell.exe",
		"notepad.exe", "mspaint.exe", "winword.exe", "excel.exe", "outlook.exe",
		"svchost.exe", "lsass.exe", "wininit.exe", "services.exe",
	}

	// Linux文件名
	linuxFiles := []string{
		"bash", "sh", "python", "perl", "java", "gcc", "g++",
		"sshd", "apache2", "nginx", "mysql", "postgres",
		"systemd", "cron", "logrotate", "rsync",
	}

	// Windows扩展名
	windowsExtensions := []string{
		".exe", ".dll", ".sys", ".drv", ".ocx",
		".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".txt", ".log", ".ini", ".cfg", ".conf",
		".jpg", ".png", ".gif", ".bmp", ".ico",
	}

	// Linux扩展名
	linuxExtensions := []string{
		"", ".sh", ".py", ".pl", ".rb", ".js",
		".conf", ".config", ".log", ".txt", ".md",
		".so", ".a", ".o", ".la",
	}

	var dirs []string
	var files []string
	var extensions []string

	switch osType {
	case "linux":
		dirs = linuxDirs
		files = linuxFiles
		extensions = linuxExtensions
	default: // windows
		dirs = windowsDirs
		files = windowsFiles
		extensions = windowsExtensions
	}

	// 生成路径
	if depth <= 0 {
		depth = 1
	}
	if depth > 5 {
		depth = 5
	}

	// 随机选择基础目录
	n, err := rand.Int(rand.Reader, big.NewInt(int64(len(dirs))))
	if err != nil {
		return "", fmt.Errorf("failed to select base directory: %w", err)
	}
	baseDir := dirs[n.Int64()]

	// 生成子目录路径
	subDirs := make([]string, 0, depth-1)
	for i := 0; i < depth-1; i++ {
		subDirNames := []string{
			"temp", "cache", "data", "config", "logs", "bin", "lib", "share",
			"app", "system", "user", "admin", "common", "local", "public",
		}
		n2, err := rand.Int(rand.Reader, big.NewInt(int64(len(subDirNames))))
		if err != nil {
			return "", fmt.Errorf("failed to select sub directory: %w", err)
		}
		subDirs = append(subDirs, subDirNames[n2.Int64()])
	}

	// 构建完整路径
	path := baseDir
	for _, subDir := range subDirs {
		path += subDir + "\\"
		if osType == "linux" {
			path = baseDir
			for _, subDir := range subDirs {
				path += subDir + "/"
			}
			break
		}
	}

	// 处理文件名
	var fileName string
	if baseName != "" {
		fileName = baseName
	} else {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(files))))
		if err != nil {
			return "", fmt.Errorf("failed to select file name: %w", err)
		}
		fileName = files[n.Int64()]
	}

	// 处理扩展名
	if extension != "" {
		if !strings.HasPrefix(extension, ".") {
			extension = "." + extension
		}
		fileName += extension
	} else if fileType == "file" && len(extensions) > 0 {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(extensions))))
		if err != nil {
			return "", fmt.Errorf("failed to select extension: %w", err)
		}
		fileName += extensions[n.Int64()]
	}

	// 构建最终路径
	if osType == "linux" {
		// 替换Windows风格的路径分隔符
		path = strings.ReplaceAll(path, "\\", "/")
		// 替换用户名占位符
		path = strings.ReplaceAll(path, "%USERNAME%", "user")
		path = strings.ReplaceAll(path, "%USER%", "user")
		// 确保路径以/开头
		if !strings.HasPrefix(path, "/") {
			path = "/" + path
		}
	} else {
		// 替换用户名占位符
		path = strings.ReplaceAll(path, "%USERNAME%", "User")
		// 替换临时目录占位符
		path = strings.ReplaceAll(path, "%TEMP%", "C:\\Windows\\Temp")
	}

	if fileType == "file" {
		path += fileName
	}

	return path, nil
}

func (s *FilePathStrategy) ValidateParams(params map[string]any) error {
	// 验证 os 参数（可选）
	if osVal, ok := params["os"]; ok {
		os, ok := osVal.(string)
		if !ok {
			return fmt.Errorf("filePath: 'os' must be a string")
		}
		if os != "windows" && os != "linux" {
			return fmt.Errorf("filePath: 'os' must be 'windows' or 'linux'")
		}
	}

	// 验证 depth 参数（可选）
	if depthVal, ok := params["depth"]; ok {
		depth, ok := depthVal.(int)
		if !ok {
			if depthFloat, ok := depthVal.(float64); ok {
				depth = int(depthFloat)
			} else {
				return fmt.Errorf("filePath: 'depth' must be an integer")
			}
		}
		if depth < 1 || depth > 10 {
			return fmt.Errorf("filePath: 'depth' must be between 1 and 10")
		}
	}

	// 验证 baseName 参数（可选）
	if baseNameVal, ok := params["baseName"]; ok {
		_, ok := baseNameVal.(string)
		if !ok {
			return fmt.Errorf("filePath: 'baseName' must be a string")
		}
	}

	// 验证 extension 参数（可选）
	if extensionVal, ok := params["extension"]; ok {
		_, ok := extensionVal.(string)
		if !ok {
			return fmt.Errorf("filePath: 'extension' must be a string")
		}
	}

	// 验证 type 参数（可选）
	if typeVal, ok := params["type"]; ok {
		fileType, ok := typeVal.(string)
		if !ok {
			return fmt.Errorf("filePath: 'type' must be a string")
		}
		if fileType != "file" && fileType != "directory" {
			return fmt.Errorf("filePath: 'type' must be 'file' or 'directory'")
		}
	}

	return nil
}

// DateTimeAfterStrategy 在基准时间上增加随机时间偏移
type DateTimeAfterStrategy struct{}

func (s *DateTimeAfterStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 参数验证已在 ValidateParams 中完成，这里直接使用
	sourceField := params["sourceField"].(string)

	// 从记录中获取源字段值作为基准时间
	var baseTime int64
	if fieldValue, exists := record[sourceField]; exists {
		// 尝试将字段值转换为int64（时间戳）
		switch v := fieldValue.(type) {
		case int64:
			baseTime = v
		case int:
			baseTime = int64(v)
		case float64:
			baseTime = int64(v)
		case string:
			// 如果是字符串，尝试解析为时间戳
			parsedTime, err := time.Parse("2006-01-02 15:04:05", v)
			if err != nil {
				// 如果不是标准时间格式，尝试解析为Unix时间戳字符串
				return 0, fmt.Errorf("dateTimeAfter: cannot parse source field value '%s' as time", v)
			}
			baseTime = parsedTime.UnixMilli()
		default:
			return 0, fmt.Errorf("dateTimeAfter: source field value must be a number or time string, got %T", fieldValue)
		}
	} else {
		// 字段不存在，使用当前时间作为基准
		baseTime = time.Now().UnixMilli()
	}

	minOffset := time.Duration(0)
	if minVal, ok := params["minOffset"].(string); ok {
		var err error
		minOffset, err = time.ParseDuration(minVal)
		if err != nil {
			return nil, fmt.Errorf("dateTimeAfter: invalid 'minOffset' duration '%s': %w", minVal, err)
		}
	}

	maxOffset := time.Minute * 5 // 默认最大偏移5分钟
	if maxVal, ok := params["maxOffset"].(string); ok {
		var err error
		maxOffset, err = time.ParseDuration(maxVal)
		if err != nil {
			return nil, fmt.Errorf("dateTimeAfter: invalid 'maxOffset' duration '%s': %w", maxVal, err)
		}
	}

	// 确保最小偏移不大于最大偏移
	if minOffset > maxOffset {
		return nil, fmt.Errorf("dateTimeAfter: 'minOffset' cannot be greater than 'maxOffset'")
	}

	// 计算偏移范围
	offsetRange := int64(maxOffset - minOffset)
	if offsetRange <= 0 {
		// 如果范围为0，直接使用最小偏移
		return time.Unix(0, baseTime*1e6+int64(minOffset)).UnixMilli(), nil
	}

	// 生成随机偏移
	n, err := rand.Int(rand.Reader, big.NewInt(offsetRange))
	if err != nil {
		return nil, fmt.Errorf("dateTimeAfter: failed to generate random offset: %w", err)
	}
	randomOffset := minOffset + time.Duration(n.Int64())

	// 计算最终时间
	finalTime := time.Unix(0, baseTime*1e6+int64(randomOffset)).UnixMilli()

	return finalTime, nil
}

func (s *DateTimeAfterStrategy) ValidateParams(params map[string]any) error {
	// 验证 sourceField 参数
	sourceFieldVal, ok := params["sourceField"]
	if !ok {
		return fmt.Errorf("dateTimeAfter: 'sourceField' parameter is required")
	}
	sourceField, ok := sourceFieldVal.(string)
	if !ok {
		return fmt.Errorf("dateTimeAfter: 'sourceField' must be a string")
	}
	if sourceField == "" {
		return fmt.Errorf("dateTimeAfter: 'sourceField' cannot be empty")
	}

	// 验证 minOffset 参数（可选）
	if minVal, ok := params["minOffset"]; ok {
		minOffset, ok := minVal.(string)
		if !ok {
			return fmt.Errorf("dateTimeAfter: 'minOffset' must be a string")
		}
		// 尝试解析持续时间
		_, err := time.ParseDuration(minOffset)
		if err != nil {
			return fmt.Errorf("dateTimeAfter: invalid 'minOffset' duration format '%s': %w", minOffset, err)
		}
	}

	// 验证 maxOffset 参数（可选）
	if maxVal, ok := params["maxOffset"]; ok {
		maxOffset, ok := maxVal.(string)
		if !ok {
			return fmt.Errorf("dateTimeAfter: 'maxOffset' must be a string")
		}
		// 尝试解析持续时间
		_, err := time.ParseDuration(maxOffset)
		if err != nil {
			return fmt.Errorf("dateTimeAfter: invalid 'maxOffset' duration format '%s': %w", maxOffset, err)
		}
	}

	return nil
}

// HashStrategy 支持真实计算和mock两种模式的哈希生成策略
type HashStrategy struct{}

func (s *HashStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 参数验证已在 ValidateParams 中完成，这里直接使用
	algorithm := params["algorithm"].(string)
	mode := "real" // 默认模式
	if m, ok := params["mode"].(string); ok {
		mode = m
	}

	switch mode {
	case "real":
		return s.generateRealHash(params, record)
	case "mock":
		return s.generateMockHash(algorithm)
	default:
		return nil, fmt.Errorf("hash: unsupported mode '%s', must be 'real' or 'mock'", mode)
	}
}

func (s *HashStrategy) generateRealHash(params map[string]any, record map[string]any) (string, error) {
	algorithm := params["algorithm"].(string)

	// 获取输入值
	var input string
	if sourceField, ok := params["sourceField"].(string); ok {
		// 从记录中获取源字段值
		if fieldValue, exists := record[sourceField]; exists {
			if str, ok := fieldValue.(string); ok {
				input = str
			} else {
				// 如果字段值不是字符串，转换为JSON字符串
				input = fmt.Sprintf("%v", fieldValue)
			}
		} else {
			// 字段不存在，使用默认种子
			input = "default_seed_for_hash_generation"
		}
	} else {
		// 使用默认种子
		input = "default_seed_for_hash_generation"
	}

	switch algorithm {
	case "md5":
		h := md5.Sum([]byte(input))
		return hex.EncodeToString(h[:]), nil
	case "sha1":
		h := sha1.Sum([]byte(input))
		return hex.EncodeToString(h[:]), nil
	case "sha256":
		h := sha256.Sum256([]byte(input))
		return hex.EncodeToString(h[:]), nil
	default:
		return "", fmt.Errorf("hash: unsupported algorithm '%s', must be 'md5', 'sha1', or 'sha256'", algorithm)
	}
}

func (s *HashStrategy) generateMockHash(algorithm string) (string, error) {
	var length int
	switch algorithm {
	case "md5":
		length = 32
	case "sha1":
		length = 40
	case "sha256":
		length = 64
	default:
		return "", fmt.Errorf("hash: unsupported algorithm '%s', must be 'md5', 'sha1', or 'sha256'", algorithm)
	}

	// 生成指定长度的随机十六进制字符串
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("hash: failed to generate random bytes: %w", err)
	}
	return hex.EncodeToString(bytes), nil
}

func (s *HashStrategy) ValidateParams(params map[string]any) error {
	// 验证 algorithm 参数
	algorithmVal, ok := params["algorithm"]
	if !ok {
		return fmt.Errorf("hash: 'algorithm' parameter is required")
	}
	algorithm, ok := algorithmVal.(string)
	if !ok {
		return fmt.Errorf("hash: 'algorithm' must be a string")
	}

	// 验证算法类型
	supportedAlgorithms := map[string]bool{
		"md5":    true,
		"sha1":   true,
		"sha256": true,
	}
	if !supportedAlgorithms[algorithm] {
		return fmt.Errorf("hash: unsupported algorithm '%s', must be 'md5', 'sha1', or 'sha256'", algorithm)
	}

	// 验证 mode 参数（可选）
	if modeVal, ok := params["mode"]; ok {
		mode, ok := modeVal.(string)
		if !ok {
			return fmt.Errorf("hash: 'mode' must be a string")
		}
		if mode != "real" && mode != "mock" {
			return fmt.Errorf("hash: 'mode' must be 'real' or 'mock'")
		}
	}

	// 验证 sourceField 参数（可选）
	if sourceFieldVal, ok := params["sourceField"]; ok {
		sourceField, ok := sourceFieldVal.(string)
		if !ok {
			return fmt.Errorf("hash: 'sourceField' must be a string")
		}
		if sourceField == "" {
			return fmt.Errorf("hash: 'sourceField' cannot be empty")
		}
	}

	return nil
}

// ExpressionStrategy 使用 expr 库执行表达式求值
type ExpressionStrategy struct{}

func (s *ExpressionStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 参数验证已在 ValidateParams 中完成，这里直接使用
	expression := params["expression"].(string)

	// 编译表达式，提供包含record变量的环境
	env := map[string]any{
		"record": record, // 使用真实的记录上下文
	}
	program, err := expr.Compile(expression, expr.Env(env))
	if err != nil {
		return nil, fmt.Errorf("expression: failed to compile expression '%s': %w", expression, err)
	}

	// 执行表达式
	result, err := expr.Run(program, env)
	if err != nil {
		return nil, fmt.Errorf("expression: failed to execute expression '%s': %w", expression, err)
	}

	return result, nil
}

func (s *ExpressionStrategy) ValidateParams(params map[string]any) error {
	// 验证 expression 参数
	expressionVal, ok := params["expression"]
	if !ok {
		return fmt.Errorf("expression: 'expression' parameter is required")
	}
	expression, ok := expressionVal.(string)
	if !ok {
		return fmt.Errorf("expression: 'expression' must be a string")
	}
	if expression == "" {
		return fmt.Errorf("expression: 'expression' cannot be empty")
	}

	// 尝试编译表达式以验证语法，提供包含record变量的环境
	env := map[string]any{
		"record": map[string]any{},
	}
	_, err := expr.Compile(expression, expr.Env(env))
	if err != nil {
		return fmt.Errorf("expression: invalid expression syntax '%s': %w", expression, err)
	}

	return nil
}

// WeightedEnumStrategy 根据给定的权重从枚举值中随机选择一个值。
// 权重越高，被选中的概率越大。
type WeightedEnumStrategy struct{}

func (s *WeightedEnumStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 参数验证已在 ValidateParams 中完成，这里直接使用
	values := params["values"].([]any)
	weightsAny := params["weights"].([]any)

	// 将 []any 转换为 []int
	weights := make([]int, len(weightsAny))
	for i, w := range weightsAny {
		weight, ok := w.(int)
		if !ok {
			// 尝试从 float64 转换（YAML 可能将数字解析为 float64）
			if weightFloat, ok := w.(float64); ok {
				weight = int(weightFloat)
			} else {
				return nil, fmt.Errorf("weight at index %d must be an integer, got %T", i, w)
			}
		}
		weights[i] = weight
	}

	// 计算总权重
	totalWeight := 0
	for _, weight := range weights {
		totalWeight += weight
	}

	// 生成随机数
	n, err := rand.Int(rand.Reader, big.NewInt(int64(totalWeight)))
	if err != nil {
		return nil, fmt.Errorf("failed to generate random number for weighted enum: %w", err)
	}
	randomWeight := int(n.Int64())

	// 根据权重选择值
	currentWeight := 0
	for i, weight := range weights {
		currentWeight += weight
		if randomWeight < currentWeight {
			return values[i], nil
		}
	}

	// 如果由于浮点数精度问题没有选中，返回最后一个值
	return values[len(values)-1], nil
}

func (s *WeightedEnumStrategy) ValidateParams(params map[string]any) error {
	valuesVal, ok := params["values"]
	if !ok {
		return fmt.Errorf("weighted_enum: 'values' parameter is required")
	}
	values, ok := valuesVal.([]any)
	if !ok {
		return fmt.Errorf("weighted_enum: 'values' must be a list")
	}
	if len(values) == 0 {
		return fmt.Errorf("weighted_enum: 'values' list cannot be empty")
	}

	weightsVal, ok := params["weights"]
	if !ok {
		return fmt.Errorf("weighted_enum: 'weights' parameter is required")
	}
	weightsAny, ok := weightsVal.([]any)
	if !ok {
		return fmt.Errorf("weighted_enum: 'weights' must be a list")
	}
	if len(weightsAny) == 0 {
		return fmt.Errorf("weighted_enum: 'weights' list cannot be empty")
	}

	// 检查 values 和 weights 的长度是否一致
	if len(values) != len(weightsAny) {
		return fmt.Errorf("weighted_enum: 'values' and 'weights' must have the same length")
	}

	// 检查权重是否都为正数，并处理类型转换
	for i, w := range weightsAny {
		var weight int
		var ok bool

		// 尝试直接获取 int
		weight, ok = w.(int)
		if !ok {
			// 尝试从 float64 转换（YAML 可能将数字解析为 float64）
			if weightFloat, ok := w.(float64); ok {
				weight = int(weightFloat)
			} else {
				return fmt.Errorf("weighted_enum: weight at index %d must be an integer, got %T", i, w)
			}
		}

		if weight <= 0 {
			return fmt.Errorf("weighted_enum: weight at index %d must be positive, got %d", i, weight)
		}
	}

	return nil
}

// TimestampStrategy 生成当前时间的Unix毫秒时间戳。
type TimestampStrategy struct{}

func (s *TimestampStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	return time.Now().UnixMilli(), nil
}

func (s *TimestampStrategy) ValidateParams(params map[string]any) error {
	// Timestamp策略不需要参数
	return nil
}

// RandomIntStrategy 生成一个在[min, max]范围内的随机整数。
type RandomIntStrategy struct{}

func (s *RandomIntStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 参数验证已在 ValidateParams 中完成，这里直接使用
	min := params["min"].(int)
	max := params["max"].(int)

	// 使用 crypto/rand 以获得更安全的随机数
	n, err := rand.Int(rand.Reader, big.NewInt(int64(max-min+1)))
	if err != nil {
		return nil, fmt.Errorf("failed to generate random int: %w", err)
	}
	return int(n.Int64()) + min, nil
}

func (s *RandomIntStrategy) ValidateParams(params map[string]any) error {
	minVal, ok := params["min"]
	if !ok {
		return fmt.Errorf("randomInt: 'min' parameter is required")
	}
	maxVal, ok := params["max"]
	if !ok {
		return fmt.Errorf("randomInt: 'max' parameter is required")
	}

	min, ok := minVal.(int)
	if !ok {
		return fmt.Errorf("randomInt: 'min' must be an integer")
	}
	max, ok := maxVal.(int)
	if !ok {
		return fmt.Errorf("randomInt: 'max' must be an integer")
	}

	if min > max {
		return fmt.Errorf("randomInt: 'min' cannot be greater than 'max'")
	}

	return nil
}

// IPv4Strategy 生成一个随机的IPv4地址。
type IPv4Strategy struct{}

func (s *IPv4Strategy) Generate(params map[string]any, record map[string]any) (any, error) {
	parts := make([]byte, 4)
	if _, err := rand.Read(parts); err != nil {
		return nil, fmt.Errorf("failed to generate random bytes for ipv4: %w", err)
	}
	return fmt.Sprintf("%d.%d.%d.%d", parts[0], parts[1], parts[2], parts[3]), nil
}

func (s *IPv4Strategy) ValidateParams(params map[string]any) error {
	// IPv4策略不需要参数
	return nil
}

// EnumStrategy 从提供的列表中随机选择一个值。
type EnumStrategy struct{}

func (s *EnumStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 参数验证已在 ValidateParams 中完成，这里直接使用
	values := params["values"].([]any)

	n, err := rand.Int(rand.Reader, big.NewInt(int64(len(values))))
	if err != nil {
		return nil, fmt.Errorf("failed to get random index for enum: %w", err)
	}
	return values[n.Int64()], nil
}

func (s *EnumStrategy) ValidateParams(params map[string]any) error {
	valuesVal, ok := params["values"]
	if !ok {
		return fmt.Errorf("enum: 'values' parameter is required")
	}
	values, ok := valuesVal.([]any)
	if !ok {
		return fmt.Errorf("enum: 'values' must be a list")
	}
	if len(values) == 0 {
		return fmt.Errorf("enum: 'values' list cannot be empty")
	}

	return nil
}

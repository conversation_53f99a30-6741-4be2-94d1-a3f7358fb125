package main

import (
	"datagen/config"
	"datagen/engine"
	_ "datagen/strategy" // 导入以执行init()函数，注册内置策略
	"encoding/json"
	"fmt"
	"log"

	"gopkg.in/yaml.v3"
)

// 数组数据模板
const arrayTemplateJSON = `
{
	"users": [
		{
			"id": "",
			"name": "",
			"email": "",
			"age": 0
		},
		{
			"id": "",
			"name": "",
			"email": "",
			"age": 0
		},
		{
			"id": "",
			"name": "",
			"email": "",
			"age": 0
		}
	],
	"metadata": {
		"total": 0,
		"generated_at": ""
	}
}
`

// 新的数组处理器配置
const arrayProcessorConfigYAML = `
rules:
  # 使用数组处理器处理数组
  - target: "users"
    action:
      type: "arrayProcessor"
      array_field: "users"
      array_context:
        # 数组级别的上下文变量
        company_id:
          type: "randomNumber"
          min: 1000
          max: 9999
        department_pool:
          type: "enum"
          values: ["Engineering", "Marketing", "Sales", "HR", "Finance"]
        base_email_domain:
          type: "fixedValue"
          value: "company.com"
      item_config:
        rules:
          # 为每个用户生成数据
          - target: "id"
            mode: "upsert"
            action:
              type: "uuid"

          - target: "name"
            mode: "upsert"
            action:
              type: "enum"
              values: ["Alice", "Bob", "Charlie", "Diana", "Eve"]

          - target: "email"
            mode: "upsert"
            action:
              type: "expression"
              expression: "record.name + '@' + record.__array_context__.base_email_domain"

          - target: "age"
            mode: "upsert"
            action:
              type: "randomNumber"
              min: 22
              max: 65

  # 更新元数据
  - target: "metadata.total"
    mode: "upsert"
    action:
      type: "fixedValue"
      value: 3

  - target: "metadata.generated_at"
    mode: "upsert"
    action:
      type: "timestamp"
`

func main() {
	log.Println("--- Array Enhancer Demo ---")

	// 1. 解析配置
	var cfg config.Config
	if err := yaml.Unmarshal([]byte(arrayProcessorConfigYAML), &cfg); err != nil {
		log.Fatalf("Failed to unmarshal config: %v", err)
	}
	log.Println("Array processor configuration loaded successfully.")

	// 2. 解析模板数据
	var template map[string]any
	if err := json.Unmarshal([]byte(arrayTemplateJSON), &template); err != nil {
		log.Fatalf("Failed to unmarshal template: %v", err)
	}
	log.Println("Template loaded successfully.")

	// 3. 创建引擎
	dataEngine, err := engine.NewDataEngine(&cfg)
	if err != nil {
		log.Fatalf("Failed to create data engine: %v", err)
	}

	// 4. 生成数据
	result, err := dataEngine.Generate(template)
	if err != nil {
		log.Fatalf("Failed to generate data: %v", err)
	}

	// 5. 打印结果
	prettyJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal result: %v", err)
	}

	log.Println("--- Generated Array Data with Enhanced Context ---")
	fmt.Println(string(prettyJSON))

	// 6. 演示如何在后续步骤中使用增强的数组
	log.Println("\n--- Demonstrating Enhanced Array Usage ---")
	demonstrateEnhancedArrayUsage(result)
}

func demonstrateEnhancedArrayUsage(data map[string]any) {
	// 检查数组是否被正确增强
	if users, ok := data["users"].([]map[string]any); ok {
		log.Printf("Found %d users in the enhanced array:", len(users))

		for i, user := range users {
			log.Printf("User %d:", i)

			// 检查是否有数组上下文
			if arrayContext, ok := user["__array_context__"].(map[string]any); ok {
				log.Printf("  - Array Context: %+v", arrayContext)
			}

			// 检查是否有数组索引
			if arrayIndex, ok := user["__array_index__"].(int); ok {
				log.Printf("  - Array Index: %d", arrayIndex)
			}

			// 显示原始字段
			log.Printf("  - Original fields: id=%v, name=%v, email=%v, age=%v",
				user["id"], user["name"], user["email"], user["age"])
		}

		// 演示如何使用增强的上下文进行进一步处理
		log.Println("\n--- Further Processing with Enhanced Context ---")
		processEnhancedUsers(users)
	}
}

func processEnhancedUsers(users []map[string]any) {
	// 使用增强的上下文为每个用户生成更多字段
	for i, user := range users {
		// 获取数组上下文
		arrayContext, hasContext := user["__array_context__"].(map[string]any)
		arrayIndex, hasIndex := user["__array_index__"].(int)

		if hasContext && hasIndex {
			// 使用上下文生成员工ID
			if companyId, ok := arrayContext["company_id"].(int64); ok {
				user["employee_id"] = companyId*1000 + int64(arrayIndex)
			}

			// 使用上下文生成邮箱
			if domain, ok := arrayContext["base_email_domain"].(string); ok {
				user["email"] = fmt.Sprintf("user%d@%s", arrayIndex, domain)
			}

			// 使用上下文分配部门
			if departmentPool, ok := arrayContext["department_pool"].(string); ok {
				user["department"] = departmentPool
			}

			log.Printf("Enhanced User %d: employee_id=%v, email=%v, department=%v",
				i, user["employee_id"], user["email"], user["department"])
		}
	}

	// 清理临时字段（可选）
	for _, user := range users {
		delete(user, "__array_context__")
		delete(user, "__array_index__")
	}

	log.Println("Temporary context fields cleaned up.")
}

package strategy

import (
	"fmt"

	"github.com/mitchellh/mapstructure"
)

// FixedValueStrategy 生成一个固定的值
type FixedValueStrategy struct {
	params *FixedValueParams
}

// FixedValueParams FixedValue策略的参数结构体
type FixedValueParams struct {
	Value any `json:"value"`
}

func (s *FixedValueStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 直接返回固定的值
	return s.params.Value, nil
}

func (s *FixedValueStrategy) ValidateParams(params map[string]any) error {
	var p FixedValueParams

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("fixedValue: failed to decode params: %w", err)
	}

	// 验证参数
	if p.Value == nil {
		return fmt.Errorf("fixedValue: 'value' cannot be nil")
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

package strategy

import (
	"crypto/rand"
	"fmt"
	"math/big"

	"github.com/mitchellh/mapstructure"
)

// WeightedEnumStrategy 根据给定的权重从枚举值中随机选择一个值。
// 权重越高，被选中的概率越大。
type WeightedEnumStrategy struct {
	params *WeightedEnumParams
}

// WeightedEnumParams WeightedEnum策略的参数结构体
type WeightedEnumParams struct {
	Values  []any `json:"values"`
	Weights []int `json:"weights"`
}

func (s *WeightedEnumStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 使用已解析的参数，假设 ValidateParams 已经正确解析
	// 计算总权重
	totalWeight := 0
	for _, weight := range s.params.Weights {
		totalWeight += weight
	}

	// 生成随机数
	n, err := rand.Int(rand.Reader, big.NewInt(int64(totalWeight)))
	if err != nil {
		return nil, fmt.Errorf("failed to generate random number for weighted enum: %w", err)
	}
	randomWeight := int(n.Int64())

	// 根据权重选择值
	currentWeight := 0
	for i, weight := range s.params.Weights {
		currentWeight += weight
		if randomWeight < currentWeight {
			return s.params.Values[i], nil
		}
	}

	// 如果由于浮点数精度问题没有选中，返回最后一个值
	return s.params.Values[len(s.params.Values)-1], nil
}

func (s *WeightedEnumStrategy) ValidateParams(params map[string]any) error {
	var p WeightedEnumParams

	// 使用 mapstructure 进行参数转换，支持弱类型转换
	decoder, err := mapstructure.NewDecoder(&mapstructure.DecoderConfig{
		Result:           &p,
		WeaklyTypedInput: true,
		TagName:          "json",
	})
	if err != nil {
		return fmt.Errorf("weighted_enum: failed to create decoder: %w", err)
	}

	if err := decoder.Decode(params); err != nil {
		return fmt.Errorf("weighted_enum: failed to decode params: %w", err)
	}

	// 验证参数
	if len(p.Values) == 0 {
		return fmt.Errorf("weighted_enum: 'values' list cannot be empty")
	}

	if len(p.Weights) == 0 {
		return fmt.Errorf("weighted_enum: 'weights' list cannot be empty")
	}

	// 检查 values 和 weights 的长度是否一致
	if len(p.Values) != len(p.Weights) {
		return fmt.Errorf("weighted_enum: 'values' and 'weights' must have the same length")
	}

	// 检查权重是否都为正数
	for i, weight := range p.Weights {
		if weight <= 0 {
			return fmt.Errorf("weighted_enum: weight at index %d must be positive, got %d", i, weight)
		}
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

package main

import (
	"datagen/config"
	"datagen/engine"
	"datagen/strategy"
	_ "datagen/strategy" // 导入以执行init()函数，注册内置策略
	"encoding/json"
	"fmt"
	"log"

	"gopkg.in/yaml.v3"
)

// 简单的配置示例
const simpleConfigYAML = `
rules:
  - target: "name"
    mode: "upsert"
    action:
      type: "fixedValue"
      value: "John Doe"

  - target: "id"
    mode: "create"
    action:
      type: "uuid"

  - target: "timestamp"
    mode: "upsert"
    action:
      type: "timestamp"
`

func main() {
	log.Println("--- Data Generation Demo ---")

	// 演示配置系统
	demonstrateConfig()

	// 列出所有可用策略
	listAvailableStrategies()
}

func demonstrateConfig() {
	log.Println("\n=== Configuration System Demo ===")

	// 1. 解析新配置
	var cfg config.Config
	if err := yaml.Unmarshal([]byte(simpleConfigYAML), &cfg); err != nil {
		log.Fatalf("Failed to unmarshal config: %v", err)
	}
	log.Println("Configuration loaded successfully.")

	// 2. 创建引擎
	dataEngine, err := engine.NewDataEngine(&cfg)
	if err != nil {
		log.Fatalf("Failed to create data engine: %v", err)
	}

	// 3. 准备测试数据
	template := map[string]any{
		"name":     "",
		"age":      0,
		"existing": "old_value",
	}

	// 4. 生成数据
	result, err := dataEngine.Generate(template)
	if err != nil {
		log.Fatalf("Failed to generate data: %v", err)
	}

	// 5. 打印结果
	prettyJSON, _ := json.MarshalIndent(result, "", "  ")
	log.Println("Generated data:")
	fmt.Println(string(prettyJSON))
}

func listAvailableStrategies() {
	log.Println("\n=== Available Strategies ===")
	strategies := strategy.ListStrategies()
	for i, name := range strategies {
		log.Printf("%d. %s", i+1, name)
	}
}

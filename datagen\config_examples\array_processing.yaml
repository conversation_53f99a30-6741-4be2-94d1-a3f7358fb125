# 数组处理配置示例
# 新的设计思路：数组增强器 + 标准引擎规则

rules:
  # 第一步：使用数组增强器为数组元素添加上下文
  - target: "users"
    action:
      type: "arrayEnhancer"
      array_field: "users"  # 指定要处理的数组字段
      array_context:
        # 数组级别的上下文变量（所有数组元素共享）
        company_id:
          type: "randomNumber"
          min: 1000
          max: 9999
        department_pool:
          type: "enum"
          values: ["Engineering", "Marketing", "Sales", "HR"]
        base_email_domain:
          type: "fixedValue"
          value: "company.com"

  # 第二步：使用标准规则处理数组中的每个元素
  # 这些规则会应用到增强后的数组元素上
  
  # 为每个用户生成ID
  - target: "users[].id"  # 数组元素字段语法
    mode: "upsert"
    action:
      type: "uuid"
      
  # 为每个用户生成名字
  - target: "users[].name"
    mode: "upsert"
    action:
      type: "enum"
      values: ["Alice", "<PERSON>", "<PERSON>", "<PERSON>", "Eve"]
      
  # 为每个用户生成邮箱（使用数组上下文）
  - target: "users[].email"
    mode: "upsert"
    action:
      type: "expression"
      expression: "record.name + '@' + record.__array_context__.base_email_domain"
      
  # 为每个用户生成年龄
  - target: "users[].age"
    mode: "upsert"
    action:
      type: "randomNumber"
      min: 22
      max: 65
      
  # 为每个用户分配部门（使用数组上下文）
  - target: "users[].department"
    mode: "upsert"
    action:
      type: "expression"
      expression: "record.__array_context__.department_pool"
      
  # 为每个用户生成员工ID（使用数组上下文和索引）
  - target: "users[].employee_id"
    mode: "create"
    action:
      type: "expression"
      expression: "record.__array_context__.company_id * 1000 + record.__array_index__"

  # 第三步：清理临时字段
  - target: "users[].__array_context__"
    mode: "update"
    action:
      type: "fixedValue"
      value: null  # 删除临时上下文字段
      
  - target: "users[].__array_index__"
    mode: "update"
    action:
      type: "fixedValue"
      value: null  # 删除临时索引字段

  # 更新元数据
  - target: "metadata.total"
    mode: "upsert"
    action:
      type: "arrayLength"
      source_field: "users"
      
  - target: "metadata.generated_at"
    mode: "upsert"
    action:
      type: "timestamp"

# 数组处理设计文档

## 设计问题分析

### 原始设计的问题

1. **重复的规则结构**: `ItemRule` 和引擎的 `Rule` 结构重复
2. **重复的处理逻辑**: 数组策略内部重新实现了引擎的规则处理逻辑
3. **维护困难**: 两套相似的代码需要同步维护
4. **不一致性**: 可能导致行为不一致
5. **硬编码假设**: 假设数组总是在特定字段中

## 新的设计方案

### 核心思想

**数组增强器 (ArrayEnhancer) + 标准引擎规则**

数组处理策略应该是一个轻量级的包装器，它的职责是：
1. 生成数组级别的上下文变量
2. 为每个数组元素添加上下文信息
3. 让标准的引擎规则来处理具体的字段修改

### 设计优势

1. **职责分离**: 数组增强器只负责上下文，字段处理由引擎负责
2. **复用引擎逻辑**: 避免重复实现规则处理逻辑
3. **一致性**: 数组元素的处理与普通记录处理完全一致
4. **灵活性**: 支持任意复杂的数组上下文生成
5. **可扩展性**: 易于添加新的上下文变量类型

## 实现细节

### ArrayEnhancerStrategy

```go
type ArrayEnhancerStrategy struct{}

type ArrayEnhancerParams struct {
    // 数组级别的上下文变量定义
    ArrayContext map[string]ActionSpec `mapstructure:"array_context"`
    
    // 数组字段路径（可选）
    ArrayField string `mapstructure:"array_field,omitempty"`
}
```

### 工作流程

1. **解析参数**: 获取数组字段路径和上下文配置
2. **提取数组**: 从记录中提取目标数组
3. **生成上下文**: 根据配置生成数组级别的上下文变量
4. **增强元素**: 为每个数组元素添加 `__array_context__` 和 `__array_index__`
5. **返回增强数组**: 供后续规则处理

### 配置示例

```yaml
rules:
  # 第一步：使用数组增强器
  - target: "users"
    action:
      type: "arrayEnhancer"
      array_field: "users"
      array_context:
        # 数组级别的上下文变量
        company_id:
          type: "randomNumber"
          min: 1000
          max: 9999
        department_pool:
          type: "enum"
          values: ["Engineering", "Marketing", "Sales"]
        base_email_domain:
          type: "fixedValue"
          value: "company.com"

  # 第二步：使用标准规则处理数组元素
  # 注意：这里展示理想的语法，实际实现可能需要调整
  - target: "users[].id"
    mode: "upsert"
    action:
      type: "uuid"
      
  - target: "users[].email"
    mode: "upsert"
    action:
      type: "expression"
      expression: "record.name + '@' + record.__array_context__.base_email_domain"
      
  - target: "users[].employee_id"
    mode: "create"
    action:
      type: "expression"
      expression: "record.__array_context__.company_id * 1000 + record.__array_index__"
```

## 使用场景

### 1. 数组元素共享上下文

```yaml
# 所有用户共享同一个公司ID和部门池
array_context:
  company_id:
    type: "randomNumber"
    min: 1000
    max: 9999
  department_pool:
    type: "enum"
    values: ["Engineering", "Marketing", "Sales"]
```

### 2. 基于索引的数据生成

```yaml
# 使用数组索引生成序列号
- target: "users[].sequence_id"
  action:
    type: "expression"
    expression: "record.__array_index__ + 1"
```

### 3. 复杂的关联数据

```yaml
# 父子关系数据生成
array_context:
  parent_id:
    type: "uuid"
    
# 每个子元素都引用同一个父ID
- target: "children[].parent_id"
  action:
    type: "expression"
    expression: "record.__array_context__.parent_id"
```

## 扩展性

### 添加新的上下文类型

只需要实现新的策略，数组增强器会自动支持：

```yaml
array_context:
  custom_data:
    type: "customStrategy"
    param1: "value1"
    param2: "value2"
```

### 支持嵌套数组

可以递归使用数组增强器：

```yaml
- target: "departments"
  action:
    type: "arrayEnhancer"
    array_context:
      dept_base_id:
        type: "randomNumber"
        min: 100
        max: 999

- target: "departments[].employees"
  action:
    type: "arrayEnhancer"
    array_context:
      emp_base_id:
        type: "expression"
        expression: "record.__array_context__.dept_base_id * 1000"
```

## 总结

新的数组处理设计通过职责分离和复用引擎逻辑，解决了原始设计的所有问题：

- ✅ **避免重复**: 不再重复实现规则处理逻辑
- ✅ **保持一致**: 数组处理与普通处理完全一致
- ✅ **易于维护**: 只需要维护一套规则处理逻辑
- ✅ **高度灵活**: 支持任意复杂的上下文生成
- ✅ **易于扩展**: 新策略自动支持数组上下文

这个设计更符合单一职责原则，提供了更好的可维护性和扩展性。

# 简单的数据生成配置示例
rules:
  # 基本字段生成
  - target: "name"
    mode: "upsert"
    action:
      type: "fixedValue"
      value: "<PERSON> Doe"
      
  - target: "id"
    mode: "create"  # 只在字段不存在时创建
    action:
      type: "uuid"
      
  - target: "timestamp"
    mode: "upsert"
    action:
      type: "timestamp"
      
  - target: "age"
    mode: "upsert"
    action:
      type: "randomNumber"
      min: 18
      max: 65
      
  # 条件生成
  - target: "status"
    when: "record.age >= 18"  # 只有成年人才有状态
    mode: "upsert"
    action:
      type: "enum"
      values: ["active", "inactive", "pending"]
      
  # 只修改已存在的字段
  - target: "existing_field"
    mode: "update"  # 只在字段存在时修改
    action:
      type: "prefixString"
      prefix: "updated_"

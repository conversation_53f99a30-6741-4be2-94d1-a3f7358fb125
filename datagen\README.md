# DataGen - 灵活的数据生成器

一个通过配置灵活生成和修改数据的Go工具。

## 重构改进

### 🎯 核心改进

1. **清晰的配置语义** - 分离了target、when、mode、action的职责
2. **明确的操作模式** - create、update、upsert、skip四种模式
3. **更好的类型安全** - 改进的策略系统和参数验证
4. **更好的扩展性** - 策略工厂模式，线程安全的注册机制

### 🔧 新的配置格式

```yaml
rules:
  - target: "user.name"           # 目标字段路径
    when: "record.age >= 18"      # 执行条件（可选）
    mode: "upsert"                # 操作模式
    action:
      type: "fixedValue"          # 策略类型
      value: "John Doe"           # 策略参数（内联）
```

### 📋 操作模式

- **create**: 只在字段不存在时创建
- **update**: 只在字段存在时修改  
- **upsert**: 存在则修改，不存在则创建（默认）
- **skip**: 字段不存在时跳过

### 🚀 使用示例

#### 基本用法

```go
import (
    "datagen/config"
    "datagen/engine"
)

// 解析配置
var cfg config.Config
yaml.Unmarshal(configData, &cfg)

// 创建引擎
dataEngine, err := engine.NewDataEngine(&cfg)

// 生成数据
result, err := dataEngine.Generate(template)

// 修改数据
modified, err := dataEngine.Modify(original)
```



### 📁 项目结构

```
datagen/
├── config/           # 配置系统
├── engine/           # 数据处理引擎
├── strategy/         # 策略实现
├── utils/            # 工具函数
├── examples/         # 示例代码
├── config_examples/  # 配置示例
└── main.go          # 演示程序
```

### 🎮 运行示例

```bash
# 基本演示
go run main.go

# 复杂示例
go run examples/security_event_demo.go
```

### 📝 配置示例

查看 `config_examples/` 目录中的配置示例：
- `simple.yaml` - 基本配置示例
- `complex.yaml` - 复杂场景配置示例

### 🔌 可用策略

- `uuid` - UUID生成
- `timestamp` - 时间戳生成
- `randomNumber` - 随机数字
- `randomString` - 随机字符串
- `fixedValue` - 固定值
- `enum` - 枚举选择
- `weighted_enum` - 加权枚举
- `expression` - 表达式计算
- `ipv4` - IPv4地址
- `filePath` - 文件路径
- `hash` - 哈希值
- `dateTimeAfter` - 相对时间
- `prefixString` - 前缀字符串
- `arrayEnhancer` - 数组增强器

### 🏗️ 架构设计

1. **配置层** - 负责配置解析和验证
2. **引擎层** - 负责数据处理逻辑
3. **策略层** - 负责具体的数据生成
4. **工具层** - 负责通用功能



package strategy

import (
	"crypto/rand"
	"fmt"
	"math/big"

	"github.com/mitchellh/mapstructure"
)

// EnumStrategy 从提供的列表中随机选择一个值。
type EnumStrategy struct {
	params *EnumParams
}

// EnumParams Enum策略的参数结构体
type EnumParams struct {
	Values []any `json:"values"`
}

func (s *EnumStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 使用已解析的参数，假设 ValidateParams 已经正确解析
	n, err := rand.Int(rand.Reader, big.NewInt(int64(len(s.params.Values))))
	if err != nil {
		return nil, fmt.Errorf("failed to get random index for enum: %w", err)
	}
	return s.params.Values[n.Int64()], nil
}

func (s *EnumStrategy) ValidateParams(params map[string]any) error {
	var p EnumParams

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("enum: failed to decode params: %w", err)
	}

	// 验证参数
	if len(p.Values) == 0 {
		return fmt.Errorf("enum: 'values' list cannot be empty")
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

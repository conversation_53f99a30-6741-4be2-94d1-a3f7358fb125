package strategy

import (
	"crypto/rand"
	"fmt"
	"math/big"

	"github.com/mitchellh/mapstructure"
)

// RandomIntStrategy 生成一个在[min, max]范围内的随机整数。
type RandomIntStrategy struct {
	params *RandomIntParams
}

// RandomIntParams RandomInt策略的参数结构体
type RandomIntParams struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

func (s *RandomIntStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 使用已解析的参数，假设 ValidateParams 已经正确解析
	n, err := rand.Int(rand.Reader, big.NewInt(int64(s.params.Max-s.params.Min+1)))
	if err != nil {
		return nil, fmt.Errorf("failed to generate random int: %w", err)
	}
	return int(n.Int64()) + s.params.Min, nil
}

func (s *RandomIntStrategy) ValidateParams(params map[string]any) error {
	var p RandomIntParams

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("randomInt: failed to decode params: %w", err)
	}

	// 验证参数
	if p.Min > p.Max {
		return fmt.Errorf("randomInt: 'min' cannot be greater than 'max'")
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

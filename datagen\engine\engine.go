package engine

import (
	"datagen/config"
	"datagen/strategy"
	"datagen/utils"
	"fmt"
	"strings"

	"github.com/expr-lang/expr"
)

// DataEngine 重构后的数据处理引擎
type DataEngine struct {
	preparedConfig *config.PreparedConfig
	fieldChecker   *FieldChecker
}

// FieldChecker 字段存在性检查器
type FieldChecker struct{}

// NewDataEngine 创建并初始化引擎
func NewDataEngine(cfg *config.Config) (*DataEngine, error) {
	preparedConfig, err := config.Prepare(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare config: %w", err)
	}

	engine := &DataEngine{
		preparedConfig: preparedConfig,
		fieldChecker:   &FieldChecker{},
	}

	return engine, nil
}

// Process 处理数据的主要接口
func (e *DataEngine) Process(records []map[string]any) ([]map[string]any, []error) {
	if len(records) == 0 {
		return nil, nil
	}

	newRecords := make([]map[string]any, len(records))
	var errs []error

	// 逐个处理记录
	for i, record := range records {
		newRecord, err := e.processRecord(record)
		if err != nil {
			errs = append(errs, fmt.Errorf("record %d: %w", i, err))
			continue
		}
		newRecords[i] = newRecord
	}

	if len(errs) > 0 {
		return nil, errs
	}
	return newRecords, nil
}

// Generate 生成数据接口
func (e *DataEngine) Generate(template map[string]any) (map[string]any, error) {
	return e.processRecord(template)
}

// Modify 修改数据接口（语义上与Generate相同，但更明确）
func (e *DataEngine) Modify(original map[string]any) (map[string]any, error) {
	return e.processRecord(original)
}

// processRecord 处理单个记录
func (e *DataEngine) processRecord(record map[string]any) (map[string]any, error) {
	// 1. 深拷贝，保证模板不可变性
	newRecord, err := utils.DeepCopy(record)
	if err != nil {
		return nil, fmt.Errorf("failed to copy record: %w", err)
	}

	// 2. 按顺序应用所有规则
	for _, rule := range e.preparedConfig.Rules {
		if err := e.processRule(newRecord, rule); err != nil {
			return nil, fmt.Errorf("error processing rule for field '%s': %w", strings.Join(rule.TargetPath, "."), err)
		}
	}

	return newRecord, nil
}

// processRule 处理单个规则
func (e *DataEngine) processRule(record map[string]any, rule config.PreparedRule) error {
	// 1. 检查条件
	if rule.Condition != nil {
		env := map[string]any{
			"record":  record,
			"context": rule.Context,
		}

		passed, err := expr.Run(rule.Condition, env)
		if err != nil {
			return fmt.Errorf("error evaluating condition: %w", err)
		}
		if p, ok := passed.(bool); !ok || !p {
			return nil // 条件不满足，跳过此规则
		}
	}

	// 2. 检查操作模式和字段存在性
	exists := e.fieldChecker.Exists(record, rule.TargetPath)

	switch rule.Mode {
	case config.ModeCreate:
		if exists {
			return nil // 字段存在，跳过创建
		}
	case config.ModeUpdate:
		if !exists {
			return nil // 字段不存在，跳过更新
		}
	case config.ModeSkip:
		if !exists {
			return nil // 字段不存在，跳过
		}
	case config.ModeUpsert:
		// 总是执行
	default:
		return fmt.Errorf("unknown operation mode: %s", rule.Mode)
	}

	// 3. 准备上下文环境
	env := e.prepareEnvironment(record, rule.Context)

	// 4. 生成新值
	newValue, err := rule.ValueStrategy.Generate(rule.ActionParams, env)
	if err != nil {
		return fmt.Errorf("error generating value: %w", err)
	}

	// 5. 设置嵌套值
	if err := utils.SetNestedValue(record, rule.TargetPath, newValue); err != nil {
		return fmt.Errorf("error setting value: %w", err)
	}

	return nil
}

// prepareEnvironment 准备策略执行环境
func (e *DataEngine) prepareEnvironment(record map[string]any, context map[string]any) map[string]any {
	env := map[string]any{
		"record": record,
	}

	// 生成上下文变量
	if len(context) > 0 {
		contextVars := make(map[string]any)
		for varName, varConfig := range context {
			if configMap, ok := varConfig.(map[string]any); ok {
				if strategyInterface, ok := configMap["strategy"]; ok {
					if strategy, ok := strategyInterface.(strategy.Strategy); ok {
						if params, ok := configMap["params"].(map[string]any); ok {
							if value, err := strategy.Generate(params, record); err == nil {
								contextVars[varName] = value
							}
						}
					}
				}
			}
		}
		env["context"] = contextVars
	}

	return env
}

// FieldChecker 方法实现
func (fc *FieldChecker) Exists(data map[string]any, path []string) bool {
	_, err := utils.GetNestedValue(data, path)
	return err == nil
}

func (fc *FieldChecker) IsEmpty(data map[string]any, path []string) bool {
	value, err := utils.GetNestedValue(data, path)
	if err != nil {
		return true
	}

	// 检查各种"空"的情况
	switch v := value.(type) {
	case nil:
		return true
	case string:
		return v == ""
	case []any:
		return len(v) == 0
	case map[string]any:
		return len(v) == 0
	default:
		return false
	}
}

package main

import (
	"datagen/config"
	"datagen/engine"
	_ "datagen/strategy" // 导入以执行init()函数，注册内置策略
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gopkg.in/yaml.v3"
)

// 性能测试配置
const performanceTestConfigYAML = `
rules:
  # 测试表达式策略的性能优化
  - target: "user_id"
    mode: "upsert"
    action:
      type: "expression"
      expression: "record.base_id + record.index * 1000"
      
  - target: "email"
    mode: "upsert"
    action:
      type: "expression"
      expression: "record.name + '@' + record.domain"
      
  - target: "full_name"
    mode: "upsert"
    action:
      type: "expression"
      expression: "record.first_name + ' ' + record.last_name"
      
  - target: "file_name"
    mode: "upsert"
    action:
      type: "expression"
      expression: "basename(record.file_path)"
      
  - target: "random_score"
    mode: "upsert"
    action:
      type: "expression"
      expression: "random(1, 100)"

  # 测试数组增强器的性能优化
  - target: "items"
    action:
      type: "arrayEnhancer"
      array_field: "items"
      array_context:
        batch_id:
          type: "randomNumber"
          min: 10000
          max: 99999
        category:
          type: "enum"
          values: ["A", "B", "C", "D", "E"]
        timestamp:
          type: "timestamp"

  # 其他策略测试
  - target: "status"
    mode: "upsert"
    action:
      type: "enum"
      values: ["active", "inactive", "pending", "suspended"]
      
  - target: "created_at"
    mode: "upsert"
    action:
      type: "timestamp"
`

func main() {
	log.Println("--- Performance Test ---")

	// 1. 解析配置
	var cfg config.Config
	if err := yaml.Unmarshal([]byte(performanceTestConfigYAML), &cfg); err != nil {
		log.Fatalf("Failed to unmarshal config: %v", err)
	}

	// 2. 创建引擎（这里会进行预编译和预创建）
	start := time.Now()
	dataEngine, err := engine.NewDataEngine(&cfg)
	if err != nil {
		log.Fatalf("Failed to create data engine: %v", err)
	}
	initTime := time.Since(start)
	log.Printf("Engine initialization time: %v", initTime)

	// 3. 准备测试数据
	testData := map[string]any{
		"base_id":    1000,
		"index":      1,
		"name":       "john",
		"domain":     "example.com",
		"first_name": "John",
		"last_name":  "Doe",
		"file_path":  "/path/to/file.txt",
		"items": []map[string]any{
			{"id": "", "name": "Item 1"},
			{"id": "", "name": "Item 2"},
			{"id": "", "name": "Item 3"},
			{"id": "", "name": "Item 4"},
			{"id": "", "name": "Item 5"},
		},
	}

	// 4. 预热（JIT优化）
	for i := 0; i < 10; i++ {
		_, err := dataEngine.Generate(testData)
		if err != nil {
			log.Fatalf("Warmup failed: %v", err)
		}
	}

	// 5. 性能测试 - 单次执行
	start = time.Now()
	result, err := dataEngine.Generate(testData)
	if err != nil {
		log.Fatalf("Failed to generate data: %v", err)
	}
	singleTime := time.Since(start)
	log.Printf("Single generation time: %v", singleTime)

	// 6. 性能测试 - 批量执行
	iterations := 1000
	start = time.Now()
	for i := 0; i < iterations; i++ {
		// 修改一些数据以避免缓存影响
		testData["index"] = i
		testData["name"] = fmt.Sprintf("user%d", i)
		
		_, err := dataEngine.Generate(testData)
		if err != nil {
			log.Fatalf("Batch generation failed at iteration %d: %v", i, err)
		}
	}
	batchTime := time.Since(start)
	avgTime := batchTime / time.Duration(iterations)
	
	log.Printf("Batch generation (%d iterations): %v", iterations, batchTime)
	log.Printf("Average time per generation: %v", avgTime)
	log.Printf("Generations per second: %.2f", float64(iterations)/batchTime.Seconds())

	// 7. 显示一个结果示例
	prettyJSON, _ := json.MarshalIndent(result, "", "  ")
	log.Println("\n--- Sample Generated Data ---")
	fmt.Println(string(prettyJSON))

	// 8. 性能总结
	log.Println("\n--- Performance Summary ---")
	log.Printf("Initialization overhead: %v", initTime)
	log.Printf("Runtime performance: %v per generation", avgTime)
	log.Printf("Throughput: %.2f generations/second", float64(iterations)/batchTime.Seconds())
	
	if avgTime < time.Millisecond {
		log.Println("✅ Excellent performance: < 1ms per generation")
	} else if avgTime < 5*time.Millisecond {
		log.Println("✅ Good performance: < 5ms per generation")
	} else if avgTime < 10*time.Millisecond {
		log.Println("⚠️  Acceptable performance: < 10ms per generation")
	} else {
		log.Println("❌ Poor performance: > 10ms per generation")
	}
}

package strategy

import (
	"fmt"
	"sync"
)

// Strategy 定义了所有值生成策略必须实现的接口。
type Strategy interface {
	// Generate 根据给定参数和记录上下文生成数据。
	Generate(params map[string]any, record map[string]any) (any, error)

	// ValidateParams 验证策略参数的有效性。
	ValidateParams(params map[string]any) error
}

// StrategyConstructor 是策略构造函数类型，用于创建新的策略实例
type StrategyConstructor func() Strategy

// StrategyFactory 策略工厂接口，提供更好的扩展性
type StrategyFactory interface {
	Register(name string, constructor StrategyConstructor) error
	Create(name string) (Strategy, error)
	List() []string
}

// DefaultStrategyFactory 默认策略工厂实现
type DefaultStrategyFactory struct {
	mu       sync.RWMutex
	registry map[string]StrategyConstructor
}

// NewStrategyFactory 创建新的策略工厂
func NewStrategyFactory() StrategyFactory {
	return &DefaultStrategyFactory{
		registry: make(map[string]StrategyConstructor),
	}
}

// Register 注册策略构造函数
func (f *DefaultStrategyFactory) Register(name string, constructor StrategyConstructor) error {
	if constructor == nil {
		return fmt.Errorf("cannot register a nil constructor")
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	if _, exists := f.registry[name]; exists {
		return fmt.Errorf("strategy already registered: %s", name)
	}

	f.registry[name] = constructor
	return nil
}

// Create 创建策略实例
func (f *DefaultStrategyFactory) Create(name string) (Strategy, error) {
	f.mu.RLock()
	constructor, exists := f.registry[name]
	f.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("unknown strategy: %s", name)
	}

	return constructor(), nil
}

// List 列出所有已注册的策略名称
func (f *DefaultStrategyFactory) List() []string {
	f.mu.RLock()
	defer f.mu.RUnlock()

	names := make([]string, 0, len(f.registry))
	for name := range f.registry {
		names = append(names, name)
	}
	return names
}

// 全局策略工厂实例
var globalFactory = NewStrategyFactory()

// Register 向全局工厂注册策略
func Register(name string, constructor StrategyConstructor) {
	if err := globalFactory.Register(name, constructor); err != nil {
		panic(err.Error())
	}
}

// CreateStrategy 从全局工厂创建策略
func CreateStrategy(name string) (Strategy, error) {
	return globalFactory.Create(name)
}

// ListStrategies 列出所有已注册的策略
func ListStrategies() []string {
	return globalFactory.List()
}

package strategy

import (
	"datagen/utils"
	"fmt"
	"strings"

	"github.com/mitchellh/mapstructure"
)

// ArrayProcessorParams 数组处理策略的参数结构
type ArrayProcessorParams struct {
	// 数组级别的上下文变量定义
	ArrayContext map[string]ActionSpec `mapstructure:"array_context"`

	// 数组元素的处理配置（使用标准的引擎配置格式）
	ItemConfig ItemConfig `mapstructure:"item_config"`

	// 数组字段路径（可选，如果不指定则处理整个record作为数组）
	ArrayField string `mapstructure:"array_field,omitempty"`
}

// ActionSpec 动作规范
type ActionSpec struct {
	Type   string         `mapstructure:"type"`
	Params map[string]any `mapstructure:",remain"` // 使用remain收集其他字段作为参数
}

// ItemConfig 数组元素配置（复用引擎的标准配置格式）
type ItemConfig struct {
	Rules []ItemRule `mapstructure:"rules"`
}

// ItemRule 数组元素规则（与引擎的Rule格式一致）
type ItemRule struct {
	Target string     `mapstructure:"target"`         // 目标字段路径
	When   string     `mapstructure:"when,omitempty"` // 执行条件
	Mode   string     `mapstructure:"mode,omitempty"` // 操作模式
	Action ActionSpec `mapstructure:"action"`         // 动作配置
}

// ArrayProcessorStrategy 数组处理策略
// 这个策略负责：
// 1. 生成数组级别的上下文变量
// 2. 为每个数组元素创建子引擎处理字段
// 3. 返回处理后的数组
type ArrayProcessorStrategy struct {
	params            *ArrayProcessorParams
	contextStrategies map[string]Strategy // 预创建的上下文策略实例
	itemEngine        *MiniEngine         // 用于处理数组元素的迷你引擎
}

// MiniEngine 迷你引擎，用于处理数组元素
type MiniEngine struct {
	rules []PreparedItemRule
}

// PreparedItemRule 预处理的元素规则
type PreparedItemRule struct {
	TargetPath    []string
	Mode          string
	ValueStrategy Strategy
	ActionParams  map[string]any
}

// ProcessItem 处理单个数组元素
func (e *MiniEngine) ProcessItem(item map[string]any, arrayContext map[string]any, index int) (map[string]any, error) {
	for _, rule := range e.rules {
		if err := e.processRule(item, rule, arrayContext, index); err != nil {
			return nil, fmt.Errorf("failed to process rule for field '%s': %w", strings.Join(rule.TargetPath, "."), err)
		}
	}
	return item, nil
}

// processRule 处理单个规则
func (e *MiniEngine) processRule(item map[string]any, rule PreparedItemRule, arrayContext map[string]any, index int) error {
	// 检查操作模式和字段存在性
	exists := fieldExists(item, rule.TargetPath)

	switch rule.Mode {
	case "create":
		if exists {
			return nil // 字段存在，跳过创建
		}
	case "update":
		if !exists {
			return nil // 字段不存在，跳过更新
		}
	case "skip":
		if !exists {
			return nil // 字段不存在，跳过
		}
	case "upsert", "":
		// 总是执行（默认模式）
	default:
		return fmt.Errorf("unknown operation mode: %s", rule.Mode)
	}

	// 准备环境变量 - 将数组上下文添加到item中，这样表达式可以访问record.__array_context__
	enhancedItem := make(map[string]any, len(item)+2)
	for k, v := range item {
		enhancedItem[k] = v
	}
	enhancedItem["__array_context__"] = arrayContext
	enhancedItem["__array_index__"] = index

	// 构建环境变量
	env := map[string]any{
		"record":        enhancedItem,
		"array_context": arrayContext,
		"index":         index,
	}

	// 生成新值
	newValue, err := rule.ValueStrategy.Generate(rule.ActionParams, env)
	if err != nil {
		return fmt.Errorf("failed to generate value: %w", err)
	}

	// 设置值
	if err := utils.SetNestedValue(item, rule.TargetPath, newValue); err != nil {
		return fmt.Errorf("failed to set value: %w", err)
	}

	return nil
}

// fieldExists 检查字段是否存在
func fieldExists(data map[string]any, path []string) bool {
	_, err := utils.GetNestedValue(data, path)
	return err == nil
}

// Generate 实现策略接口的Generate方法
func (s *ArrayProcessorStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 获取实际的记录数据（引擎传递的是环境变量格式）
	actualRecord := record
	if recordData, ok := record["record"].(map[string]any); ok {
		actualRecord = recordData
	}

	// 获取目标数组
	var originalArray []map[string]any
	var err error

	if s.params.ArrayField != "" {
		// 从指定字段获取数组
		originalArray, err = s.extractArrayFromField(actualRecord, s.params.ArrayField)
		if err != nil {
			return nil, fmt.Errorf("failed to extract array from field '%s': %w", s.params.ArrayField, err)
		}
	} else {
		// 整个record就是数组的容器，查找数组字段
		originalArray, err = s.extractArrayFromRecord(actualRecord)
		if err != nil {
			return nil, fmt.Errorf("failed to extract array from record: %w", err)
		}
	}

	// 生成数组级别的上下文
	arrayContext, err := s.generateArrayContext(len(originalArray))
	if err != nil {
		return nil, fmt.Errorf("failed to generate array context: %w", err)
	}

	// 处理每个数组元素
	processedArray := make([]map[string]any, len(originalArray))
	for index, item := range originalArray {
		// 深拷贝元素
		processedItem, err := utils.DeepCopy(item)
		if err != nil {
			return nil, fmt.Errorf("failed to copy item %d: %w", index, err)
		}

		// 使用迷你引擎处理元素
		if s.itemEngine != nil {
			processedItem, err = s.itemEngine.ProcessItem(processedItem, arrayContext, index)
			if err != nil {
				return nil, fmt.Errorf("failed to process item %d: %w", index, err)
			}
		}

		processedArray[index] = processedItem
	}

	return processedArray, nil
}

// ValidateParams 验证策略参数
func (s *ArrayProcessorStrategy) ValidateParams(params map[string]any) error {
	if params == nil {
		return fmt.Errorf("params cannot be nil")
	}

	// 解析参数到结构体进行验证
	var arrayParams ArrayProcessorParams
	if err := mapstructure.Decode(params, &arrayParams); err != nil {
		return fmt.Errorf("failed to decode arrayProcessor params: %w", err)
	}

	// 预创建并验证所有上下文策略
	contextStrategies := make(map[string]Strategy)
	for key, actionSpec := range arrayParams.ArrayContext {
		if actionSpec.Type == "" {
			return fmt.Errorf("array_context.%s.type must be specified", key)
		}

		// 创建策略实例
		strategy, err := CreateStrategy(actionSpec.Type)
		if err != nil {
			return fmt.Errorf("array_context.%s: invalid strategy '%s': %w", key, actionSpec.Type, err)
		}

		// 验证策略参数
		if err := strategy.ValidateParams(actionSpec.Params); err != nil {
			return fmt.Errorf("array_context.%s: failed to validate params: %w", key, err)
		}

		contextStrategies[key] = strategy
	}

	// 预创建迷你引擎
	var itemEngine *MiniEngine
	if len(arrayParams.ItemConfig.Rules) > 0 {
		rules := make([]PreparedItemRule, len(arrayParams.ItemConfig.Rules))
		for i, rule := range arrayParams.ItemConfig.Rules {
			// 解析目标路径
			targetPath := strings.Split(rule.Target, ".")

			// 创建策略实例
			strategy, err := CreateStrategy(rule.Action.Type)
			if err != nil {
				return fmt.Errorf("item_config.rules[%d]: invalid strategy '%s': %w", i, rule.Action.Type, err)
			}

			// 验证策略参数
			if err := strategy.ValidateParams(rule.Action.Params); err != nil {
				return fmt.Errorf("item_config.rules[%d]: failed to validate params: %w", i, err)
			}

			// 设置默认模式
			mode := rule.Mode
			if mode == "" {
				mode = "upsert"
			}

			rules[i] = PreparedItemRule{
				TargetPath:    targetPath,
				Mode:          mode,
				ValueStrategy: strategy,
				ActionParams:  rule.Action.Params,
			}
		}

		itemEngine = &MiniEngine{rules: rules}
	}

	// 存储预处理结果
	s.params = &arrayParams
	s.contextStrategies = contextStrategies
	s.itemEngine = itemEngine
	return nil
}

// extractArrayFromField 从指定字段提取数组
func (s *ArrayProcessorStrategy) extractArrayFromField(record map[string]any, fieldPath string) ([]map[string]any, error) {
	path := strings.Split(fieldPath, ".")
	value, err := utils.GetNestedValue(record, path)
	if err != nil {
		return nil, fmt.Errorf("field '%s' not found: %w", fieldPath, err)
	}

	return s.convertToMapArray(value)
}

// extractArrayFromRecord 从record中自动查找数组字段
func (s *ArrayProcessorStrategy) extractArrayFromRecord(record map[string]any) ([]map[string]any, error) {
	// 查找第一个数组字段
	for _, value := range record {
		if array, err := s.convertToMapArray(value); err == nil {
			return array, nil
		}
	}

	return nil, fmt.Errorf("no array field found in record")
}

// convertToMapArray 将任意类型转换为[]map[string]any
func (s *ArrayProcessorStrategy) convertToMapArray(value any) ([]map[string]any, error) {
	// 直接是[]map[string]any
	if mapArray, ok := value.([]map[string]any); ok {
		return mapArray, nil
	}

	// 是[]any，需要转换
	if anyArray, ok := value.([]any); ok {
		result := make([]map[string]any, len(anyArray))
		for i, item := range anyArray {
			if itemMap, ok := item.(map[string]any); ok {
				result[i] = itemMap
			} else {
				return nil, fmt.Errorf("array item %d is not a map, got %T", i, item)
			}
		}
		return result, nil
	}

	return nil, fmt.Errorf("value is not an array, got %T", value)
}

// generateArrayContext 生成数组级别的上下文
func (s *ArrayProcessorStrategy) generateArrayContext(arraySize int) (map[string]any, error) {
	arrayContext := make(map[string]any)

	// 使用预创建的策略实例
	for key, strategy := range s.contextStrategies {
		// 生成值，提供数组级别的环境
		env := map[string]any{
			"array_size":    arraySize,
			"array_context": arrayContext, // 已生成的上下文变量
		}

		// 获取对应的参数
		actionSpec := s.params.ArrayContext[key]
		value, err := strategy.Generate(actionSpec.Params, env)
		if err != nil {
			return nil, fmt.Errorf("failed to generate array context variable '%s': %w", key, err)
		}
		arrayContext[key] = value
	}

	return arrayContext, nil
}

// 注册策略
func init() {
	Register("arrayProcessor", func() Strategy {
		return &ArrayProcessorStrategy{}
	})
}

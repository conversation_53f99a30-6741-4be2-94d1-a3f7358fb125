package strategy

import (
	"crypto/rand"
	"fmt"
	"math/big"

	"github.com/mitchellh/mapstructure"
)

// RandomNumberStrategy 生成一个指定范围内的随机数字（支持整数和浮点数）
type RandomNumberStrategy struct {
	params *RandomNumberParams
}

// RandomNumberParams RandomNumber策略的参数结构体
type RandomNumberParams struct {
	Min     float64 `json:"min"`
	Max     float64 `json:"max"`
	Decimal int     `json:"decimal"` // 小数位数，0表示整数
}

func (s *RandomNumberStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	if s.params.Decimal == 0 {
		// 生成整数
		minInt := int64(s.params.Min)
		maxInt := int64(s.params.Max)
		if minInt > maxInt {
			minInt, maxInt = maxInt, minInt
		}

		// 检查范围是否有效
		if maxInt == minInt {
			return minInt, nil
		}
		if maxInt < minInt {
			return nil, fmt.Errorf("randomNumber: invalid range, max (%d) < min (%d)", maxInt, minInt)
		}

		rangeSize := maxInt - minInt + 1
		if rangeSize <= 0 {
			return nil, fmt.Errorf("randomNumber: range size overflow or invalid")
		}

		n, err := rand.Int(rand.Reader, big.NewInt(rangeSize))
		if err != nil {
			return nil, fmt.Errorf("failed to generate random integer: %w", err)
		}
		return n.Int64() + minInt, nil
	} else {
		// 生成浮点数
		minFloat := s.params.Min
		maxFloat := s.params.Max
		if minFloat > maxFloat {
			minFloat, maxFloat = maxFloat, minFloat
		}

		// 检查范围是否有效
		if maxFloat == minFloat {
			return minFloat, nil
		}

		// 生成 [0, 1) 范围内的随机数
		n, err := rand.Int(rand.Reader, big.NewInt(1<<53))
		if err != nil {
			return nil, fmt.Errorf("failed to generate random float: %w", err)
		}
		randomFloat := float64(n.Int64()) / (1 << 53)

		// 缩放到指定范围
		result := minFloat + randomFloat*(maxFloat-minFloat)

		// 四舍五入到指定小数位
		multiplier := float64(1)
		for i := 0; i < s.params.Decimal; i++ {
			multiplier *= 10
		}
		result = float64(int64(result*multiplier+0.5)) / multiplier

		return result, nil
	}
}

func (s *RandomNumberStrategy) ValidateParams(params map[string]any) error {
	var p RandomNumberParams

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("randomNumber: failed to decode params: %w", err)
	}

	// 验证参数
	if p.Decimal < 0 {
		return fmt.Errorf("randomNumber: 'decimal' cannot be negative")
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

package strategy

import (
	"crypto/rand"
	"fmt"
	"math/big"

	"github.com/mitchellh/mapstructure"
)

// PrefixStringStrategy 生成一个固定前缀加上随机长度字符串的组合
type PrefixStringStrategy struct {
	params *PrefixStringParams
}

// PrefixStringParams PrefixString策略的参数结构体
type PrefixStringParams struct {
	Prefix    string `json:"prefix"`    // 固定前缀
	MinLength int    `json:"minLength"` // 随机部分最小长度
	MaxLength int    `json:"maxLength"` // 随机部分最大长度
	Charset   string `json:"charset"`   // 字符集
}

func (s *PrefixStringStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 生成随机部分的长度
	if s.params.MinLength == s.params.MaxLength {
		randomPart, err := s.generateRandomString(s.params.MinLength)
		if err != nil {
			return nil, err
		}
		return s.params.Prefix + randomPart, nil
	}

	lengthRange := s.params.MaxLength - s.params.MinLength + 1
	n, err := rand.Int(rand.Reader, big.NewInt(int64(lengthRange)))
	if err != nil {
		return nil, fmt.Errorf("failed to generate random length: %w", err)
	}

	length := int(n.Int64()) + s.params.MinLength
	randomPart, err := s.generateRandomString(length)
	if err != nil {
		return nil, err
	}

	return s.params.Prefix + randomPart, nil
}

func (s *PrefixStringStrategy) generateRandomString(length int) (string, error) {
	if length <= 0 {
		return "", nil
	}

	charset := s.params.Charset
	if charset == "" {
		// 默认字符集：字母+数字
		charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	}

	result := make([]byte, length)
	charsetLength := big.NewInt(int64(len(charset)))

	for i := 0; i < length; i++ {
		n, err := rand.Int(rand.Reader, charsetLength)
		if err != nil {
			return "", fmt.Errorf("failed to generate random character: %w", err)
		}
		result[i] = charset[n.Int64()]
	}

	return string(result), nil
}

func (s *PrefixStringStrategy) ValidateParams(params map[string]any) error {
	var p PrefixStringParams

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("prefixString: failed to decode params: %w", err)
	}

	// 验证参数
	if p.MinLength < 0 {
		return fmt.Errorf("prefixString: 'minLength' cannot be negative")
	}
	if p.MaxLength < 0 {
		return fmt.Errorf("prefixString: 'maxLength' cannot be negative")
	}
	if p.MinLength > p.MaxLength {
		return fmt.Errorf("prefixString: 'minLength' cannot be greater than 'maxLength'")
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

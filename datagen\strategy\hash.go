package strategy

import (
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/hex"
	"fmt"

	"github.com/mitchellh/mapstructure"
)

// HashStrategy 支持真实计算和mock两种模式的哈希生成策略
type HashStrategy struct {
	params *HashParams
}

// HashParams Hash策略的参数结构体
type HashParams struct {
	Algorithm   string `json:"algorithm"`
	Mode        string `json:"mode"`
	SourceField string `json:"sourceField"`
}

func (s *HashStrategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// 使用已解析的参数，假设 ValidateParams 已经正确解析
	switch s.params.Mode {
	case "real":
		return s.generateRealHash(record)
	case "mock":
		return s.generateMockHash()
	default:
		return nil, fmt.Errorf("hash: unsupported mode '%s', must be 'real' or 'mock'", s.params.Mode)
	}
}

func (s *HashStrategy) generateRealHash(record map[string]any) (string, error) {
	// 获取输入值
	var input string
	if s.params.SourceField != "" {
		// 从记录中获取源字段值
		if fieldValue, exists := record[s.params.SourceField]; exists {
			if str, ok := fieldValue.(string); ok {
				input = str
			} else {
				// 如果字段值不是字符串，转换为JSON字符串
				input = fmt.Sprintf("%v", fieldValue)
			}
		} else {
			// 字段不存在，使用默认种子
			input = "default_seed_for_hash_generation"
		}
	} else {
		// 使用默认种子
		input = "default_seed_for_hash_generation"
	}

	switch s.params.Algorithm {
	case "md5":
		h := md5.Sum([]byte(input))
		return hex.EncodeToString(h[:]), nil
	case "sha1":
		h := sha1.Sum([]byte(input))
		return hex.EncodeToString(h[:]), nil
	case "sha256":
		h := sha256.Sum256([]byte(input))
		return hex.EncodeToString(h[:]), nil
	default:
		return "", fmt.Errorf("hash: unsupported algorithm '%s', must be 'md5', 'sha1', or 'sha256'", s.params.Algorithm)
	}
}

func (s *HashStrategy) generateMockHash() (string, error) {
	var length int
	switch s.params.Algorithm {
	case "md5":
		length = 32
	case "sha1":
		length = 40
	case "sha256":
		length = 64
	default:
		return "", fmt.Errorf("hash: unsupported algorithm '%s', must be 'md5', 'sha1', or 'sha256'", s.params.Algorithm)
	}

	// 生成指定长度的随机十六进制字符串
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("hash: failed to generate random bytes: %w", err)
	}
	return hex.EncodeToString(bytes), nil
}

func (s *HashStrategy) ValidateParams(params map[string]any) error {
	var p HashParams

	// 设置默认值
	p.Mode = "real"

	// 使用 mapstructure 进行参数转换
	if err := mapstructure.Decode(params, &p); err != nil {
		return fmt.Errorf("hash: failed to decode params: %w", err)
	}

	// 验证参数
	if p.Algorithm == "" {
		return fmt.Errorf("hash: 'algorithm' parameter is required")
	}

	// 验证算法类型
	supportedAlgorithms := map[string]bool{
		"md5":    true,
		"sha1":   true,
		"sha256": true,
	}
	if !supportedAlgorithms[p.Algorithm] {
		return fmt.Errorf("hash: unsupported algorithm '%s', must be 'md5', 'sha1', or 'sha256'", p.Algorithm)
	}

	if p.Mode != "real" && p.Mode != "mock" {
		return fmt.Errorf("hash: 'mode' must be 'real' or 'mock'")
	}

	// 存储解析后的参数
	s.params = &p
	return nil
}

package strategy

import (
	"crypto/rand"
	"fmt"
)

// IPv4Strategy 生成一个随机的IPv4地址。
type IPv4Strategy struct{}

func (s *IPv4Strategy) Generate(params map[string]any, record map[string]any) (any, error) {
	// IPv4策略不需要参数
	parts := make([]byte, 4)
	if _, err := rand.Read(parts); err != nil {
		return nil, fmt.Errorf("failed to generate random bytes for ipv4: %w", err)
	}
	return fmt.Sprintf("%d.%d.%d.%d", parts[0], parts[1], parts[2], parts[3]), nil
}

func (s *IPv4Strategy) ValidateParams(params map[string]any) error {
	// IPv4策略不需要参数
	return nil
}
